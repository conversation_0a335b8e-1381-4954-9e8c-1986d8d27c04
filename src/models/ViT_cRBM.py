import numpy as np
import jax
import jax.numpy as jnp
import flax.linen as nn
import math
from typing import Union, Any
from netket.utils.types import NNInitFunc
from netket.nn import log_cosh
from jax.nn.initializers import normal, xavier_uniform

default_kernel_init = normal(stddev=0.01)

class ModelConfig:
    """ViT-cRBM混合模型参数"""
    # cRBM相关参数
    alpha = 2                    # RBM隐藏单元比例（降低以平衡复杂度）
    use_hidden_bias = True       # 是否使用隐藏偏置
    use_visible_bias = True      # 是否使用可见偏置
    
    # ViT相关参数
    num_layers = 6               # 注意力层数（降低以平衡复杂度）
    d_model = 64                 # 嵌入维度
    heads = 8                    # 多头注意力头数
    patch_size = 2               # patch大小
    
    # 通用参数
    param_dtype = "complex128"   # 参数数据类型
    use_symmetries = True        # 是否使用对称性
    
class TrainingConfig:
    """训练参数"""
    seed = 0
    learning_rate = 0.08         # 学习率（介于cRBM和ViT之间）
    n_iter = 15000              # 总迭代次数
    n_train = 1                 # 每次退火的训练步数
    n_samples = 2**13           # 样本数量
    n_discard_per_chain = 0     # 每条链丢弃的样本数
    chunk_size = 2**10          # 批处理大小
    diag_shift = 0.05           # 对角线位移
    clip_norm = 1.0             # 梯度裁剪阈值
    
    # 热重启余弦退火参数
    initial_period = 100        # 初始周期长度
    period_mult = 2.0           # 周期倍数
    max_temperature = 1.0       # 最大温度
    min_temperature = 0.0       # 最小温度

class cRBMFeatureExtractor(nn.Module):
    """
    cRBM特征提取器：提取最近邻关联子特征
    基于cRBM模型中的关联子计算逻辑
    """
    Lx: int                                      # x方向格点数
    Ly: int                                      # y方向格点数
    param_dtype: Any = np.complex128             # 参数数据类型
    activation: Any = log_cosh                   # 激活函数
    alpha: Union[float, int] = 2                 # 隐藏单元比例
    use_hidden_bias: bool = True                 # 是否使用隐藏偏置
    use_visible_bias: bool = True                # 是否使用可见偏置
    kernel_init: NNInitFunc = default_kernel_init
    hidden_bias_init: NNInitFunc = default_kernel_init
    visible_bias_init: NNInitFunc = default_kernel_init

    @nn.compact
    def __call__(self, input):
        """
        提取cRBM风格的关联子特征
        Args:
            input: 输入自旋构型 [batch_size, n_sites]
        Returns:
            enhanced_features: 增强的特征 [batch_size, enhanced_feature_dim]
        """
        input_b = input
        l_b = jnp.size(input_b, axis=0)  # batch_size
        
        # 重塑为二维格点阵列 [batch_size, Lx, 2*Ly]
        # 这里2*Ly是因为蜂窝晶格每个单元格有2个格点
        input_c = jnp.reshape(input_b, (l_b, self.Lx, 2 * self.Ly))
        
        # 转置为 [batch_size, 2*Ly, Lx]
        input_d = jnp.transpose(input_c, (0, 2, 1))
        l_d = jnp.size(input_d, axis=0)
        
        # 重塑为 [batch_size, Ly, 2*Lx]
        input_e = jnp.reshape(input_d, (l_d, self.Ly, 2 * self.Lx))
        
        # 对最后一个维度排序
        input_f = jnp.sort(input_e, axis=-1)

        # 计算x方向的最近邻关联子
        input_c_roll = jnp.roll(input_c, -1, axis=-1)
        input_cc_roll = input_c * input_c_roll
        input_cc_roll_final = jnp.reshape(input_cc_roll, (l_b, 2 * self.Lx * self.Ly))

        # 计算y方向的最近邻关联子
        input_f_roll = jnp.roll(input_f, -1, axis=-1)
        input_ff_roll = input_f * input_f_roll
        input_ff_roll_final = jnp.reshape(input_ff_roll, (l_b, 2 * self.Lx * self.Ly))

        # 删除部分关联子以避免重复计数
        input_ff_roll_final_2 = jnp.delete(input_ff_roll_final, slice(None, None, 2), axis=1)

        # 合并所有关联子
        input_final_nn_correlators = jnp.concatenate((input_cc_roll_final, input_ff_roll_final_2), axis=-1)

        # 合并原始自旋和关联子
        input_final = jnp.concatenate((input_b, input_final_nn_correlators), axis=-1)

        # 通过一个较小的隐藏层处理特征（降低复杂度）
        x = nn.Dense(
            name="FeatureEnhancer",
            features=int(self.alpha * input_final.shape[-1]),
            param_dtype=self.param_dtype,
            use_bias=self.use_hidden_bias,
            kernel_init=self.kernel_init,
            bias_init=self.hidden_bias_init,
        )(input_final)
        
        x = self.activation(x)
        
        # 可见偏置
        if self.use_visible_bias:
            v_bias = self.param(
                "visible_bias",
                self.visible_bias_init,
                (input_final.shape[-1],),
                self.param_dtype,
            )
            bias_contribution = jnp.dot(input_final, v_bias)
            # 将偏置贡献广播到隐藏层维度
            bias_contribution = jnp.expand_dims(bias_contribution, axis=-1)
            x = x + bias_contribution
        
        return x  # [batch_size, enhanced_feature_dim]

class PatchEmbedding(nn.Module):
    """Patch嵌入层：将增强特征映射为高维向量"""
    d_model: int
    patch_size: int
    param_dtype: jnp.dtype = jnp.complex128

    def setup(self):
        self.patch_projection = nn.Dense(
            self.d_model,
            param_dtype=self.param_dtype,
            dtype=self.param_dtype,
            kernel_init=xavier_uniform()
        )

    def __call__(self, x):
        """
        Args:
            x: [batch_size, enhanced_feature_dim] 增强特征
        Returns:
            [batch_size, n_patches, d_model] patch嵌入后的张量
        """
        batch_size, feature_dim = x.shape

        # 确保feature_dim能被patch_size整除
        assert feature_dim % self.patch_size == 0, f"feature_dim ({feature_dim}) must be divisible by patch_size ({self.patch_size})"

        n_patches = feature_dim // self.patch_size

        # 重塑为patches: [batch_size, feature_dim] -> [batch_size, n_patches, patch_size]
        x_patches = x.reshape(batch_size, n_patches, self.patch_size)

        # 对每个patch进行嵌入
        embedded = self.patch_projection(x_patches)

        return embedded

class RelativePositionalEncoding(nn.Module):
    """相对位置编码模块"""
    n_patches: int
    param_dtype: jnp.dtype = jnp.complex128

    def setup(self):
        self.pos_encoding = self.param(
            'pos_encoding',
            xavier_uniform(),
            (self.n_patches, self.n_patches),
            self.param_dtype
        )

    def __call__(self):
        return self.pos_encoding

class MultiHeadSelfAttention(nn.Module):
    """多头自注意力模块，支持复数参数"""
    d_model: int
    heads: int
    n_patches: int
    param_dtype: jnp.dtype = jnp.complex128

    def setup(self):
        assert self.d_model % self.heads == 0
        self.d_head = self.d_model // self.heads

        # Q, K, V 线性变换层
        self.w_q = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype,
                           use_bias=False, kernel_init=xavier_uniform())
        self.w_k = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype,
                           use_bias=False, kernel_init=xavier_uniform())
        self.w_v = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype,
                           use_bias=False, kernel_init=xavier_uniform())

        # 相对位置编码
        self.rpe = RelativePositionalEncoding(n_patches=self.n_patches, param_dtype=self.param_dtype)

        # 输出投影
        self.w_o = nn.Dense(self.d_model, param_dtype=self.param_dtype, dtype=self.param_dtype,
                           use_bias=False, kernel_init=xavier_uniform())

    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_patches, d_model] 输入
        Returns:
            [batch_size, n_patches, d_model] 注意力输出
        """
        batch_size, n_patches, d_model = x.shape

        # 生成 Q, K, V
        q = self.w_q(x).reshape(batch_size, n_patches, self.heads, self.d_head)
        k = self.w_k(x).reshape(batch_size, n_patches, self.heads, self.d_head)
        v = self.w_v(x).reshape(batch_size, n_patches, self.heads, self.d_head)

        # 转置为 [batch_size, heads, n_patches, d_head]
        q = jnp.transpose(q, (0, 2, 1, 3))
        k = jnp.transpose(k, (0, 2, 1, 3))
        v = jnp.transpose(v, (0, 2, 1, 3))

        # 计算注意力分数：Q K^T + 相对位置编码
        # 对于复数，使用共轭转置
        k_conj = jnp.conj(k)
        scores = jnp.einsum('bhid,bhjd->bhij', q, k_conj) / jnp.sqrt(self.d_head)

        # 添加相对位置编码
        rpe_scores = self.rpe()[None, None, :, :]
        # 限制相对位置编码的幅度（分别处理实部和虚部）
        rpe_real = jnp.clip(jnp.real(rpe_scores), -10.0, 10.0)
        rpe_imag = jnp.clip(jnp.imag(rpe_scores), -10.0, 10.0)
        rpe_scores = rpe_real + 1j * rpe_imag
        scores = scores + rpe_scores

        # 限制注意力分数，避免 softmax 溢出
        # 对复数取实部进行softmax
        scores_real = jnp.real(scores)
        scores_real = jnp.clip(scores_real, -30.0, 30.0)

        # 应用 softmax 和注意力权重
        attention_weights = nn.softmax(scores_real, axis=-1)
        # 将权重转换为复数（虚部为0）
        attention_weights = attention_weights.astype(self.param_dtype)
        attention_output = jnp.einsum('bhij,bhjd->bhid', attention_weights, v)

        # 重塑并合并所有头
        attention_output = jnp.transpose(attention_output, (0, 2, 1, 3))
        attention_output = attention_output.reshape(batch_size, n_patches, d_model)

        return self.w_o(attention_output)

class AttentionLayer(nn.Module):
    """注意力层：多头自注意力 + 残差连接 + 归一化"""
    d_model: int
    heads: int
    n_patches: int
    param_dtype: jnp.dtype = jnp.complex128

    def setup(self):
        self.mhsa = MultiHeadSelfAttention(
            d_model=self.d_model,
            heads=self.heads,
            n_patches=self.n_patches,
            param_dtype=self.param_dtype
        )
        self.layer_norm = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
        # 添加输出缩放因子以防止梯度爆炸
        self.output_scale = 0.1

    def __call__(self, x):
        # 前归一化 + 残差连接
        normalized_x = self.layer_norm(x)
        attention_output = self.mhsa(normalized_x)
        # 缩放注意力输出并加上残差连接
        return x + self.output_scale * attention_output

class ComplexOutput(nn.Module):
    """复数输出层 - 直接输出复数对数波函数"""
    d_model: int
    param_dtype: jnp.dtype = jnp.complex128

    def setup(self):
        self.final_projection = nn.Dense(
            1,  # 直接输出复数值
            param_dtype=self.param_dtype,
            dtype=self.param_dtype,
            # 使用更小的初始化以防止输出过大
            kernel_init=normal(stddev=0.01)
        )

    def __call__(self, x):
        """
        Args:
            x: [batch_size, n_patches, d_model] 注意力输出
        Returns:
            复数对数波函数值 [batch_size,]
        """
        # 线性投影到复数值
        complex_contrib = self.final_projection(x)  # [batch_size, n_patches, 1]

        # 求和所有patch的贡献
        output = jnp.sum(complex_contrib[:, :, 0], axis=1)  # [batch_size,]

        # 限制输出幅度，避免过大的波函数值
        output_real = jnp.clip(jnp.real(output), -20.0, 20.0)
        output_imag = jnp.clip(jnp.imag(output), -20.0, 20.0)

        # 组合成复数对数波函数
        return output_real + 1j * output_imag

class ViT_cRBM(nn.Module):
    """
    Vision Transformer + cRBM 混合神经量子态模型

    结合了cRBM的关联子特征提取能力和ViT的注意力机制

    架构流程：
    Input → cRBM Feature Extraction → Patch Embedding → Multi-layer Attention → Complex Output → log ψ

    Args:
        Lx, Ly: 晶格尺寸
        num_layers: 注意力层数
        d_model: 嵌入维度和模型维度
        heads: 多头注意力的头数
        patch_size: patch大小
        alpha: cRBM隐藏单元比例
        param_dtype: 参数数据类型
    """
    Lx: int
    Ly: int
    num_layers: int
    d_model: int
    heads: int
    patch_size: int
    alpha: Union[float, int] = 2
    param_dtype: jnp.dtype = jnp.complex128
    use_hidden_bias: bool = True
    use_visible_bias: bool = True



    @nn.compact
    def __call__(self, input):
        """
        前向传播
        Args:
            input: 输入自旋构型 [batch_size, n_sites]
        Returns:
            对数波函数值（复数）[batch_size,]
        """
        # 第一阶段：cRBM特征提取
        crbm_extractor = cRBMFeatureExtractor(
            Lx=self.Lx,
            Ly=self.Ly,
            param_dtype=self.param_dtype,
            alpha=self.alpha,
            use_hidden_bias=self.use_hidden_bias,
            use_visible_bias=self.use_visible_bias
        )
        enhanced_features = crbm_extractor(input)  # [batch_size, enhanced_feature_dim]

        # 动态计算patch数量
        enhanced_feature_dim = enhanced_features.shape[-1]
        assert enhanced_feature_dim % self.patch_size == 0, f"Enhanced feature dim ({enhanced_feature_dim}) must be divisible by patch_size ({self.patch_size})"
        n_patches = enhanced_feature_dim // self.patch_size

        # 第二阶段：Patch嵌入
        patch_embedding = PatchEmbedding(
            d_model=self.d_model,
            patch_size=self.patch_size,
            param_dtype=self.param_dtype
        )
        x = patch_embedding(enhanced_features)  # [batch_size, n_patches, d_model]

        # 第三阶段：多层注意力处理
        for i in range(self.num_layers):
            attention_layer = AttentionLayer(
                d_model=self.d_model,
                heads=self.heads,
                n_patches=n_patches,
                param_dtype=self.param_dtype
            )
            x = attention_layer(x)

        # 第四阶段：最终归一化
        final_norm = nn.LayerNorm(dtype=self.param_dtype, param_dtype=self.param_dtype)
        x = final_norm(x)

        # 第五阶段：复数输出
        complex_output = ComplexOutput(
            d_model=self.d_model,
            param_dtype=self.param_dtype
        )
        return complex_output(x)
