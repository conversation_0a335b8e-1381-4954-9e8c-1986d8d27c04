#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kitaev_model运行器模块
提供了一种更面向对象的方式来进行Kitaev模型模拟
"""

import os
import sys
import time
import json
import numpy as np
import jax
import jax.numpy as jnp
import netket as nk
import netket.optimizer as nk_opt
import flax

from src.utils.FE_VMC_SRt import CustomFreeEnergyVMC_SRt
from src.utils.logging import log_message
from src.physics.kitaev import (
    create_honeycomb_lattice, 
    create_kitaev_hamiltonian, 
    get_symmetries,
    save_lattice_figure
)

class KitaevRunner:
    """Kitaev模型运行器类，提供面向对象的接口"""
    
    def __init__(self, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda, 
                 model_type=None, model_class=None, model_config=None, training_config=None, 
                 reference_energy=None, result_dir=None):
        """
        初始化运行器
        
        Args:
            Lx, Ly: 晶格尺寸
            Kx, Ky, Kz: Kitaev相互作用强度
            J: Heisenberg相互作用强度
            hx, hy, hz: [111]方向磁场
            Lambda: plaquette算符的拉格朗日乘子
            model_type: 模型类型 ("cRBM" 或 "ViT")
            model_class: 模型类
            model_config: 模型配置类
            training_config: 训练配置类
            reference_energy: 参考能量，用于显示相对误差
            result_dir: 结果保存目录，如果为None则自动生成
        """
        self.Lx = Lx
        self.Ly = Ly
        self.Kx = Kx
        self.Ky = Ky
        self.Kz = Kz
        self.J = J
        self.hx = hx
        self.hy = hy
        self.hz = hz
        self.Lambda = Lambda
        self.reference_energy = reference_energy
        
        # 存储模型相关信息
        self.model_type = model_type or "cRBM"
        self.model_class = model_class
        self.model_config = model_config
        self.training_config = training_config
        
        # 如果没有提供配置，使用默认的cRBM配置
        if self.model_config is None or self.training_config is None:
            from src.models.cRBM import ModelConfig as cRBM_ModelConfig, TrainingConfig as cRBM_TrainingConfig
            from src.models.cRBM import cRBM
            self.model_config = cRBM_ModelConfig
            self.training_config = cRBM_TrainingConfig
            self.model_class = cRBM
            self.model_type = "cRBM"
        
        # 创建结果目录，包含模型类型信息
        if result_dir is None:
            self.result_dir = f"results/{self.model_type}/Lx={Lx}_Ly={Ly}/Kx={Kx}_Ky={Ky}_Kz={Kz}/J={J}/h={hx}_{hy}_{hz}/Lambda={Lambda}"
        else:
            self.result_dir = result_dir
            
        self.training_dir = os.path.join(self.result_dir, "training")
        self.analysis_dir = os.path.join(self.result_dir, "analysis")
        
        # 创建所有必要的目录
        os.makedirs(self.training_dir, exist_ok=True)
        os.makedirs(self.analysis_dir, exist_ok=True)
        
        # 设置日志文件 - 训练日志保存在training目录
        self.energy_log = os.path.join(self.training_dir, 
                                      f"energy_{self.model_type}_Lx={Lx}_Ly={Ly}_Kx={Kx}_Ky={Ky}_Kz={Kz}_J={J}_h={hx}_{hy}_{hz}_Lambda={Lambda}.log")
        
        # 初始化物理系统
        self.lattice = create_honeycomb_lattice(Lx, Ly)
        self.N = self.lattice.n_nodes
        
        # 保存晶格图像到training目录
        lattice_filename = os.path.join(self.training_dir, "Honeycomb_lattice.png")
        save_lattice_figure(self.lattice, lattice_filename)
        
        # 记录开始信息
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"Kitaev-Heisenberg Model Study")
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"Model type: {self.model_type}")
        log_message(self.energy_log, f"System parameters: Lx={Lx}, Ly={Ly}, N={self.N}")
        log_message(self.energy_log, f"  - Kitaev interactions: Kx={Kx}, Ky={Ky}, Kz={Kz}")
        log_message(self.energy_log, f"  - Heisenberg interaction: J={J}")
        log_message(self.energy_log, f"  - Magnetic field [111]: hx={hx}, hy={hy}, hz={hz}")
        log_message(self.energy_log, f"  - Lagrange multiplier: Lambda={Lambda}")
        log_message(self.energy_log, f"  - Reference energy: {self.reference_energy}")
    
    def setup_model(self):
        """设置模型和优化器"""
        # 创建Hamiltonian
        self.H, self.hi = create_kitaev_hamiltonian(
            self.lattice, self.Kx, self.Ky, self.Kz, self.J, 
            self.hx, self.hy, self.hz, self.Lambda
        )
        
        # 设置采样器
        self.sampler = nk.sampler.MetropolisLocal(
            hilbert=self.hi,
            n_chains=self.training_config.n_samples
        )
        
        # 根据模型类型设置模型
        if self.model_type.lower() == "crbm":
            model_no_symm = self.model_class(
                Lx=self.Lx,
                Ly=self.Ly,
                alpha=self.model_config.alpha,
                param_dtype=getattr(np, self.model_config.param_dtype),
                use_hidden_bias=self.model_config.use_hidden_bias,
                use_visible_bias=self.model_config.use_visible_bias
            )
        elif self.model_type.lower() == "vit":
            model_no_symm = self.model_class(
                num_layers=self.model_config.num_layers,
                d_model=self.model_config.d_model,
                heads=self.model_config.heads,
                patch_size=self.model_config.patch_size,
                n_sites=self.N,
                param_dtype=getattr(jnp, self.model_config.param_dtype)
            )
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
        
        # 获取对称性
        symmetries = get_symmetries(self.lattice)
        
        # 根据ModelConfig中的use_symmetries参数决定是否使用对称性
        if self.model_config.use_symmetries:
            # 使用对称性
            self.model = nk.nn.blocks.SymmExpSum(
                module=model_no_symm, 
                symm_group=symmetries, 
                character_id=0
            )
            symmetries_used = len(symmetries)
        else:
            # 不使用对称性，直接使用原始模型
            self.model = model_no_symm
            symmetries_used = 0
        
        # 创建变分量子态
        self.vqs = nk.vqs.MCState(
            sampler=self.sampler,
            model=self.model,
            n_samples=self.training_config.n_samples,
            n_discard_per_chain=self.training_config.n_discard_per_chain,
            chunk_size=self.training_config.chunk_size,
        )
        
        # 记录模型参数
        n_params = nk.jax.tree_size(self.vqs.parameters)
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Model parameters:")
        log_message(self.energy_log, f"  - Model type: {self.model_type}")
        
        if self.model_type.lower() == "crbm":
            log_message(self.energy_log, f"  - Alpha: {self.model_config.alpha}")
            log_message(self.energy_log, f"  - Use hidden bias: {self.model_config.use_hidden_bias}")
            log_message(self.energy_log, f"  - Use visible bias: {self.model_config.use_visible_bias}")
        elif self.model_type.lower() == "vit":
            log_message(self.energy_log, f"  - Layers: {self.model_config.num_layers}")
            log_message(self.energy_log, f"  - Model dimension: {self.model_config.d_model}")
            log_message(self.energy_log, f"  - Attention heads: {self.model_config.heads}")
            log_message(self.energy_log, f"  - Patch size: {self.model_config.patch_size}")
        
        log_message(self.energy_log, f"  - Parameter dtype: {self.model_config.param_dtype}")
        log_message(self.energy_log, f"  - Use symmetries: {self.model_config.use_symmetries}")
        log_message(self.energy_log, f"  - Symmetries used: {symmetries_used}")
        log_message(self.energy_log, f"  - Total parameters: {n_params}")
        
        # 记录训练参数
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Training parameters:")
        log_message(self.energy_log, f"  - Learning rate: {self.training_config.learning_rate}")
        log_message(self.energy_log, f"  - Total annealing steps: {self.training_config.n_iter}")
        log_message(self.energy_log, f"  - Samples: {self.training_config.n_samples}")
        log_message(self.energy_log, f"  - Discarded samples: {self.training_config.n_discard_per_chain}")
        log_message(self.energy_log, f"  - Chunk size: {self.training_config.chunk_size}")
        log_message(self.energy_log, f"  - Temperature range: [{self.training_config.min_temperature}, {self.training_config.max_temperature}]")
        log_message(self.energy_log, f"  - Restart period: {self.training_config.initial_period}")
        log_message(self.energy_log, f"  - Period multiplier: {self.training_config.period_mult}")
        log_message(self.energy_log, f"  - Gradient clipping: {getattr(self.training_config, 'grad_clip', 1.0)}")
        
        # 记录设备状态
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Device status:")
        log_message(self.energy_log, f"  - Number of devices: {len(jax.devices())}")
        log_message(self.energy_log, f"  - Device type: {jax.devices()[0].device_kind}")
        log_message(self.energy_log, f"  - NetKet experimental sharding: {nk.config.netket_experimental_sharding}")
        
        # 保存参数
        params_dict = {
            "model_type": self.model_type,
            "seed": self.training_config.seed,
            "learning_rate": self.training_config.learning_rate,
            "n_iter": self.training_config.n_iter,
            "n_train": self.training_config.n_train,
            "n_samples": self.training_config.n_samples,
            "n_discard_per_chain": self.training_config.n_discard_per_chain,
            "chunk_size": self.training_config.chunk_size,
            "max_temperature": self.training_config.max_temperature,
            "min_temperature": self.training_config.min_temperature,
            "initial_period": self.training_config.initial_period,
            "period_mult": self.training_config.period_mult,
            "param_dtype": self.model_config.param_dtype,
            "reference_energy": self.reference_energy,
            "symmetries_used": symmetries_used,
            "Lx": self.Lx,
            "Ly": self.Ly,
            "N": self.N,
            "Kx": self.Kx,
            "Ky": self.Ky,
            "Kz": self.Kz,
            "J": self.J,
            "hx": self.hx,
            "hy": self.hy,
            "hz": self.hz,
            "Lambda": self.Lambda,
            "lattice_extent": [self.Lx, self.Ly],
            "lattice_pbc": [True, True]
        }
        
        # 添加模型特定参数
        if self.model_type.lower() == "crbm":
            params_dict.update({
                "alpha": self.model_config.alpha,
                "use_hidden_bias": self.model_config.use_hidden_bias,
                "use_visible_bias": self.model_config.use_visible_bias,
                "use_symmetries": self.model_config.use_symmetries
            })
        elif self.model_type.lower() == "vit":
            params_dict.update({
                "num_layers": self.model_config.num_layers,
                "d_model": self.model_config.d_model,
                "heads": self.model_config.heads,
                "patch_size": self.model_config.patch_size,
                "use_symmetries": self.model_config.use_symmetries
            })
        
        params_file = os.path.join(self.training_dir, 
                                  f"parameters_{self.model_type}_Lx={self.Lx}_Ly={self.Ly}_Kx={self.Kx}_Ky={self.Ky}_Kz={self.Kz}_J={self.J}_h={self.hx}_{self.hy}_{self.hz}_Lambda={self.Lambda}.json")
        with open(params_file, "w") as f_out:
            json.dump(params_dict, f_out, indent=4)
        
        return self
    
    def run(self, n_iter=None, n_train=None):
        """
        运行模拟
        
        Args:
            n_iter: 迭代次数，如果为None则使用配置中的值
            n_train: 每次迭代的训练步数，如果为None则使用配置中的值
        """
        if n_iter is None:
            n_iter = self.training_config.n_iter
        if n_train is None:
            n_train = self.training_config.n_train
            
        log_message(self.energy_log, "-"*50)
        log_message(self.energy_log, "Start training...")
        
        # 记录时间
        start = time.time()
        
        # 创建优化器
        optimizer = nk_opt.Sgd(learning_rate=self.training_config.learning_rate)
        
        # 使用新的 CustomFreeEnergyVMC_SRt 方法，传递热重启参数
        clip_norm = getattr(self.training_config, 'grad_clip', 1.0)  # 默认梯度裁剪阈值为1.0
        vmc = CustomFreeEnergyVMC_SRt(
            reference_energy=self.reference_energy,
            initial_period=self.training_config.initial_period,
            period_mult=self.training_config.period_mult,
            max_temperature=self.training_config.max_temperature,
            min_temperature=self.training_config.min_temperature,
            clip_norm=clip_norm,
            hamiltonian=self.H,
            optimizer=optimizer,
            diag_shift=self.training_config.diag_shift,
            variational_state=self.vqs
        )
        
        # 运行优化
        vmc.run(n_iter=n_iter, energy_log=self.energy_log)
        
        end = time.time()
        
        runtime = end - start
        log_message(self.energy_log, "="*50)
        log_message(self.energy_log, f"Training finished, total running time = {runtime:.2f} seconds")
        
        # 保存最终状态到training目录
        import pickle
        state_file = os.path.join(self.training_dir, 
                                 f"{self.model_type}_final_Lx={self.Lx}_Ly={self.Ly}_Kx={self.Kx}_Ky={self.Ky}_Kz={self.Kz}_J={self.J}_h={self.hx}_{self.hy}_{self.hz}_Lambda={self.Lambda}.pkl")
        with open(state_file, "wb") as f_state:
            pickle.dump(self.vqs.parameters, f_state)
        log_message(self.energy_log, f"The trained quantum state parameters have been saved to: {state_file}")
        log_message(self.energy_log, "="*50)
        
        return self
    
    @classmethod
    def run_simulation(cls, Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda):
        """
        静态方法：运行单个模拟 (向后兼容)
        
        Args:
            所有Kitaev模型参数
        """
        runner = cls(Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda)
        runner.setup_model()
        runner.run()
        return runner 