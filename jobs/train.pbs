#!/bin/sh

#PBS -q normal
#PBS -l select=1:ngpus=2
#PBS -l walltime=22:00:00
#PBS -P 12004256
###PBS -P personal-s240076
#PBS -N kitaev-model
#PBS -j oe

# ==================== 模型选择 ====================
# 指定要使用的模型: "cRBM", "ViT", 或 "ViT_cRBM"
MODEL_TYPE="ViT_cRBM"

# ==================== 物理系统参数 ====================
# 系统大小
LX_VALUE=4
LY_VALUE=4

# Kitaev相互作用参数
KX_VALUES="1.0"
KY_VALUES="1.0" 
KZ_VALUES="1.0"

# Heisenberg相互作用
J_VALUES="0.0"

# [111]磁场
HX_VALUES="0.1"
HY_VALUES="0.1"
HZ_VALUES="0.1"

# 拉格朗日乘子
LAMBDA_VALUES="0.0"

# 自旋
SPIN_VALUE=0.5

# 参考能量 (用于显示相对误差，设为"None"则不显示)
REFERENCE_ENERGY="-6.396"

# ==================== 作业执行 ====================
# 进入工作目录
cd $PBS_O_WORKDIR || exit $?

# 记录作业开始时间和节点信息
echo "Job start at: $(date)"
echo "Running on node: $(hostname)"
echo "GPU Information:"
nvidia-smi

# 加载必要的模块
module load singularity

echo "==================== Job Configuration ===================="
echo "Model Type: $MODEL_TYPE"
echo "System Size: Lx=$LX_VALUE, Ly=$LY_VALUE"
echo "Spin: $SPIN_VALUE"
echo "Processing the following parameter combinations:"
echo "Kx values: $KX_VALUES"
echo "Ky values: $KY_VALUES"
echo "Kz values: $KZ_VALUES"
echo "J values: $J_VALUES"
echo "hx values: $HX_VALUES"
echo "hy values: $HY_VALUES"
echo "hz values: $HZ_VALUES"
echo "Lambda values: $LAMBDA_VALUES"
echo "Reference Energy: $REFERENCE_ENERGY"
echo "=========================================================="

# 并行任务最大数量
max_tasks=6
current_tasks=0

for Kx in $KX_VALUES; do
    for Ky in $KY_VALUES; do
        for Kz in $KZ_VALUES; do
            for J in $J_VALUES; do
                for hx in $HX_VALUES; do
                    for hy in $HY_VALUES; do
                        for hz in $HZ_VALUES; do
                            for Lambda in $LAMBDA_VALUES; do
                                echo "Starting computation Model=$MODEL_TYPE, Lx=$LX_VALUE, Ly=$LY_VALUE, Spin=$SPIN_VALUE, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda, RefE=$REFERENCE_ENERGY at: $(date)"
                                
                                # 提交任务到后台运行，传递模型类型和所有参数
                                singularity exec --nv -B /scratch,/app \
                                    /home/<USER>/ntu/s240076/Repositories/Jupyter_server/config/netket.sif \
                                    python scripts/train.py $MODEL_TYPE $LX_VALUE $LY_VALUE $SPIN_VALUE $Kx $Ky $Kz $J $hx $hy $hz $Lambda $REFERENCE_ENERGY 2>&1 &
                                
                                current_tasks=$((current_tasks + 1))
                                
                                # 如果达到最大并行任务数，则等待这批任务全部结束，再继续提交
                                if [ $current_tasks -ge $max_tasks ]; then
                                    wait
                                    current_tasks=0
                                fi
                                
                                echo "Submitted job Model=$MODEL_TYPE, Lx=$LX_VALUE, Ly=$LY_VALUE, Spin=$SPIN_VALUE, Kx=$Kx, Ky=$Ky, Kz=$Kz, J=$J, hx=$hx, hy=$hy, hz=$hz, Lambda=$Lambda, RefE=$REFERENCE_ENERGY at: $(date)"
                            done
                        done
                    done
                done
            done
        done
    done
done

# 等待剩余任务
wait

echo "Job finished at: $(date)" 