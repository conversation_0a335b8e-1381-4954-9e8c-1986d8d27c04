[2025-06-05 12:00:05] ==================================================
[2025-06-05 12:00:05] Kitaev-Heisenberg Model Study
[2025-06-05 12:00:05] ==================================================
[2025-06-05 12:00:05] Model type: cRBM
[2025-06-05 12:00:05] System parameters: Lx=4, Ly=4, N=32
[2025-06-05 12:00:05]   - Kitaev interactions: Kx=1.0, Ky=1.0, Kz=1.0
[2025-06-05 12:00:05]   - Heisenberg interaction: J=0.0
[2025-06-05 12:00:05]   - Magnetic field [111]: hx=0.1, hy=0.1, hz=0.1
[2025-06-05 12:00:05]   - Lagrange multiplier: Lambda=0.0
[2025-06-05 12:00:05]   - Reference energy: -6.396
[2025-06-05 12:00:09] --------------------------------------------------
[2025-06-05 12:00:09] Model parameters:
[2025-06-05 12:00:09]   - Model type: cRBM
[2025-06-05 12:00:09]   - Alpha: 4
[2025-06-05 12:00:09]   - Use hidden bias: True
[2025-06-05 12:00:09]   - Use visible bias: True
[2025-06-05 12:00:09]   - Parameter dtype: complex128
[2025-06-05 12:00:09]   - Use symmetries: True
[2025-06-05 12:00:09]   - Symmetries used: 32
[2025-06-05 12:00:09]   - Total parameters: 26000
[2025-06-05 12:00:09] --------------------------------------------------
[2025-06-05 12:00:09] Training parameters:
[2025-06-05 12:00:09]   - Learning rate: 0.1
[2025-06-05 12:00:09]   - Total annealing steps: 20000
[2025-06-05 12:00:09]   - Samples: 8192
[2025-06-05 12:00:09]   - Discarded samples: 0
[2025-06-05 12:00:09]   - Chunk size: 1024
[2025-06-05 12:00:09]   - Temperature range: [0.0, 1.0]
[2025-06-05 12:00:09]   - Restart period: 100
[2025-06-05 12:00:09]   - Period multiplier: 2.0
[2025-06-05 12:00:09]   - Gradient clipping: 1.0
[2025-06-05 12:00:09] --------------------------------------------------
[2025-06-05 12:00:09] Device status:
[2025-06-05 12:00:09]   - Number of devices: 1
[2025-06-05 12:00:09]   - Device type: NVIDIA A100-SXM4-40GB
[2025-06-05 12:00:09]   - NetKet experimental sharding: False
[2025-06-05 12:00:09] --------------------------------------------------
[2025-06-05 12:00:09] Start training...
[2025-06-05 12:00:12] Starting training with Cosine Annealing with Warm Restarts:
[2025-06-05 12:00:12] Initial period: 100, Period multiplier: 2.0, Temperature range: [0.0000, 1.0000]
[2025-06-05 12:00:12] ============================================================
[2025-06-05 12:00:24] Iter: 1/20000, R0[0/100], Temp: 1.0000, Energy: 2.408794+0.001526j, Rel_err(%): 137.6610
[2025-06-05 12:00:27] Iter: 2/20000, R0[1/100], Temp: 0.9998, Energy: 1.598992-0.008852j, Rel_err(%): 125.0000
[2025-06-05 12:00:30] Iter: 3/20000, R0[2/100], Temp: 0.9990, Energy: 0.411750-0.010942j, Rel_err(%): 106.4378
[2025-06-05 12:00:33] Iter: 4/20000, R0[3/100], Temp: 0.9978, Energy: -0.854697-0.006462j, Rel_err(%): 86.6371
[2025-06-05 12:00:36] Iter: 5/20000, R0[4/100], Temp: 0.9961, Energy: -2.051807-0.007505j, Rel_err(%): 67.9206
[2025-06-05 12:00:40] Iter: 6/20000, R0[5/100], Temp: 0.9938, Energy: -3.115580-0.009652j, Rel_err(%): 51.2888
[2025-06-05 12:00:43] Iter: 7/20000, R0[6/100], Temp: 0.9911, Energy: -3.874108-0.004706j, Rel_err(%): 39.4293
[2025-06-05 12:00:46] Iter: 8/20000, R0[7/100], Temp: 0.9880, Energy: -4.482422-0.014957j, Rel_err(%): 29.9193
[2025-06-05 12:00:49] Iter: 9/20000, R0[8/100], Temp: 0.9843, Energy: -4.869868+0.005738j, Rel_err(%): 23.8609
[2025-06-05 12:00:52] Iter: 10/20000, R0[9/100], Temp: 0.9801, Energy: -5.123181-0.001145j, Rel_err(%): 19.9002
[2025-06-05 12:00:55] Iter: 11/20000, R0[10/100], Temp: 0.9755, Energy: -5.318080-0.007804j, Rel_err(%): 16.8535
[2025-06-05 12:00:58] Iter: 12/20000, R0[11/100], Temp: 0.9704, Energy: -5.473643-0.008790j, Rel_err(%): 14.4215
[2025-06-05 12:01:01] Iter: 13/20000, R0[12/100], Temp: 0.9649, Energy: -5.603778-0.002232j, Rel_err(%): 12.3863
[2025-06-05 12:01:04] Iter: 14/20000, R0[13/100], Temp: 0.9589, Energy: -5.696044-0.001138j, Rel_err(%): 10.9437
[2025-06-05 12:01:07] Iter: 15/20000, R0[14/100], Temp: 0.9524, Energy: -5.750967+0.000791j, Rel_err(%): 10.0850
[2025-06-05 12:01:10] Iter: 16/20000, R0[15/100], Temp: 0.9455, Energy: -5.817488-0.000234j, Rel_err(%): 9.0449
[2025-06-05 12:01:13] Iter: 17/20000, R0[16/100], Temp: 0.9382, Energy: -5.866145-0.003275j, Rel_err(%): 8.2843
[2025-06-05 12:01:16] Iter: 18/20000, R0[17/100], Temp: 0.9304, Energy: -5.902921+0.001246j, Rel_err(%): 7.7092
[2025-06-05 12:01:20] Iter: 19/20000, R0[18/100], Temp: 0.9222, Energy: -5.933793+0.000891j, Rel_err(%): 7.2265
[2025-06-05 12:01:23] Iter: 20/20000, R0[19/100], Temp: 0.9135, Energy: -5.963868+0.001742j, Rel_err(%): 6.7563
[2025-06-05 12:01:26] Iter: 21/20000, R0[20/100], Temp: 0.9045, Energy: -5.977811-0.002064j, Rel_err(%): 6.5384
[2025-06-05 12:01:29] Iter: 22/20000, R0[21/100], Temp: 0.8951, Energy: -5.997830+0.003866j, Rel_err(%): 6.2256
[2025-06-05 12:01:32] Iter: 23/20000, R0[22/100], Temp: 0.8853, Energy: -6.014143-0.002612j, Rel_err(%): 5.9704
[2025-06-05 12:01:35] Iter: 24/20000, R0[23/100], Temp: 0.8751, Energy: -6.022140-0.002847j, Rel_err(%): 5.8454
[2025-06-05 12:01:38] Iter: 25/20000, R0[24/100], Temp: 0.8645, Energy: -6.041525-0.003006j, Rel_err(%): 5.5423
[2025-06-05 12:01:41] Iter: 26/20000, R0[25/100], Temp: 0.8536, Energy: -6.037967-0.000910j, Rel_err(%): 5.5978
[2025-06-05 12:01:44] Iter: 27/20000, R0[26/100], Temp: 0.8423, Energy: -6.052769-0.000489j, Rel_err(%): 5.3663
[2025-06-05 12:01:47] Iter: 28/20000, R0[27/100], Temp: 0.8307, Energy: -6.051801-0.001482j, Rel_err(%): 5.3815
[2025-06-05 12:01:50] Iter: 29/20000, R0[28/100], Temp: 0.8187, Energy: -6.077785+0.009044j, Rel_err(%): 4.9772
[2025-06-05 12:01:53] Iter: 30/20000, R0[29/100], Temp: 0.8065, Energy: -6.078531+0.001375j, Rel_err(%): 4.9636
[2025-06-05 12:01:57] Iter: 31/20000, R0[30/100], Temp: 0.7939, Energy: -6.078416-0.001296j, Rel_err(%): 4.9654
[2025-06-05 12:02:00] Iter: 32/20000, R0[31/100], Temp: 0.7810, Energy: -6.093902-0.001291j, Rel_err(%): 4.7233
[2025-06-05 12:02:03] Iter: 33/20000, R0[32/100], Temp: 0.7679, Energy: -6.103476-0.006534j, Rel_err(%): 4.5747
[2025-06-05 12:02:06] Iter: 34/20000, R0[33/100], Temp: 0.7545, Energy: -6.101734+0.001714j, Rel_err(%): 4.6009
[2025-06-05 12:02:09] Iter: 35/20000, R0[34/100], Temp: 0.7409, Energy: -6.113990+0.005452j, Rel_err(%): 4.4100
[2025-06-05 12:02:12] Iter: 36/20000, R0[35/100], Temp: 0.7270, Energy: -6.107018-0.001554j, Rel_err(%): 4.5182
[2025-06-05 12:02:15] Iter: 37/20000, R0[36/100], Temp: 0.7129, Energy: -6.119926-0.004789j, Rel_err(%): 4.3170
[2025-06-05 12:02:18] Iter: 38/20000, R0[37/100], Temp: 0.6986, Energy: -6.120684+0.004005j, Rel_err(%): 4.3050
[2025-06-05 12:02:21] Iter: 39/20000, R0[38/100], Temp: 0.6841, Energy: -6.123336-0.003243j, Rel_err(%): 4.2633
[2025-06-05 12:02:24] Iter: 40/20000, R0[39/100], Temp: 0.6694, Energy: -6.124169+0.002304j, Rel_err(%): 4.2502
[2025-06-05 12:02:28] Iter: 41/20000, R0[40/100], Temp: 0.6545, Energy: -6.124211+0.001827j, Rel_err(%): 4.2495
[2025-06-05 12:02:31] Iter: 42/20000, R0[41/100], Temp: 0.6395, Energy: -6.129175-0.000895j, Rel_err(%): 4.1718
[2025-06-05 12:02:34] Iter: 43/20000, R0[42/100], Temp: 0.6243, Energy: -6.135565-0.000267j, Rel_err(%): 4.0718
[2025-06-05 12:02:37] Iter: 44/20000, R0[43/100], Temp: 0.6091, Energy: -6.136020-0.004750j, Rel_err(%): 4.0654
[2025-06-05 12:02:40] Iter: 45/20000, R0[44/100], Temp: 0.5937, Energy: -6.141599-0.000239j, Rel_err(%): 3.9775
[2025-06-05 12:02:43] Iter: 46/20000, R0[45/100], Temp: 0.5782, Energy: -6.146623+0.002080j, Rel_err(%): 3.8991
[2025-06-05 12:02:46] Iter: 47/20000, R0[46/100], Temp: 0.5627, Energy: -6.149641-0.004297j, Rel_err(%): 3.8524
[2025-06-05 12:02:49] Iter: 48/20000, R0[47/100], Temp: 0.5471, Energy: -6.147730+0.000768j, Rel_err(%): 3.8817
[2025-06-05 12:02:52] Iter: 49/20000, R0[48/100], Temp: 0.5314, Energy: -6.149226-0.000870j, Rel_err(%): 3.8583
[2025-06-05 12:02:56] Iter: 50/20000, R0[49/100], Temp: 0.5157, Energy: -6.151710-0.004332j, Rel_err(%): 3.8200
[2025-06-05 12:02:59] Iter: 51/20000, R0[50/100], Temp: 0.5000, Energy: -6.152242-0.002411j, Rel_err(%): 3.8113
[2025-06-05 12:03:02] Iter: 52/20000, R0[51/100], Temp: 0.4843, Energy: -6.153137+0.001208j, Rel_err(%): 3.7972
[2025-06-05 12:03:05] Iter: 53/20000, R0[52/100], Temp: 0.4686, Energy: -6.158022-0.002354j, Rel_err(%): 3.7209
[2025-06-05 12:03:08] Iter: 54/20000, R0[53/100], Temp: 0.4529, Energy: -6.164153+0.001333j, Rel_err(%): 3.6249
[2025-06-05 12:03:11] Iter: 55/20000, R0[54/100], Temp: 0.4373, Energy: -6.160819+0.000928j, Rel_err(%): 3.6770
[2025-06-05 12:03:14] Iter: 56/20000, R0[55/100], Temp: 0.4218, Energy: -6.166327-0.000726j, Rel_err(%): 3.5909
[2025-06-05 12:03:18] Iter: 57/20000, R0[56/100], Temp: 0.4063, Energy: -6.163519+0.002953j, Rel_err(%): 3.6351
[2025-06-05 12:03:21] Iter: 58/20000, R0[57/100], Temp: 0.3909, Energy: -6.167778+0.003551j, Rel_err(%): 3.5686
[2025-06-05 12:03:24] Iter: 59/20000, R0[58/100], Temp: 0.3757, Energy: -6.172207+0.002137j, Rel_err(%): 3.4991
[2025-06-05 12:03:27] Iter: 60/20000, R0[59/100], Temp: 0.3605, Energy: -6.161713+0.000326j, Rel_err(%): 3.6630
[2025-06-05 12:03:30] Iter: 61/20000, R0[60/100], Temp: 0.3455, Energy: -6.174618+0.000910j, Rel_err(%): 3.4613
[2025-06-05 12:03:33] Iter: 62/20000, R0[61/100], Temp: 0.3306, Energy: -6.171857-0.001038j, Rel_err(%): 3.5045
[2025-06-05 12:03:36] Iter: 63/20000, R0[62/100], Temp: 0.3159, Energy: -6.182273+0.003740j, Rel_err(%): 3.3421
[2025-06-05 12:03:40] Iter: 64/20000, R0[63/100], Temp: 0.3014, Energy: -6.185783+0.000094j, Rel_err(%): 3.2867
[2025-06-05 12:03:43] Iter: 65/20000, R0[64/100], Temp: 0.2871, Energy: -6.186755+0.001439j, Rel_err(%): 3.2716
[2025-06-05 12:03:46] Iter: 66/20000, R0[65/100], Temp: 0.2730, Energy: -6.174938+0.002823j, Rel_err(%): 3.4565
[2025-06-05 12:03:49] Iter: 67/20000, R0[66/100], Temp: 0.2591, Energy: -6.181427+0.000463j, Rel_err(%): 3.3548
[2025-06-05 12:03:52] Iter: 68/20000, R0[67/100], Temp: 0.2455, Energy: -6.191186-0.004908j, Rel_err(%): 3.2031
[2025-06-05 12:03:55] Iter: 69/20000, R0[68/100], Temp: 0.2321, Energy: -6.186551-0.003820j, Rel_err(%): 3.2752
[2025-06-05 12:03:58] Iter: 70/20000, R0[69/100], Temp: 0.2190, Energy: -6.193579+0.000433j, Rel_err(%): 3.1648
[2025-06-05 12:04:01] Iter: 71/20000, R0[70/100], Temp: 0.2061, Energy: -6.186471-0.000968j, Rel_err(%): 3.2760
[2025-06-05 12:04:05] Iter: 72/20000, R0[71/100], Temp: 0.1935, Energy: -6.195660+0.001293j, Rel_err(%): 3.1323
[2025-06-05 12:04:08] Iter: 73/20000, R0[72/100], Temp: 0.1813, Energy: -6.195719+0.002248j, Rel_err(%): 3.1315
[2025-06-05 12:04:11] Iter: 74/20000, R0[73/100], Temp: 0.1693, Energy: -6.194967+0.004814j, Rel_err(%): 3.1440
[2025-06-05 12:04:14] Iter: 75/20000, R0[74/100], Temp: 0.1577, Energy: -6.196451+0.001864j, Rel_err(%): 3.1200
[2025-06-05 12:04:17] Iter: 76/20000, R0[75/100], Temp: 0.1464, Energy: -6.202317-0.001372j, Rel_err(%): 3.0283
[2025-06-05 12:04:20] Iter: 77/20000, R0[76/100], Temp: 0.1355, Energy: -6.200365+0.000270j, Rel_err(%): 3.0587
[2025-06-05 12:04:24] Iter: 78/20000, R0[77/100], Temp: 0.1249, Energy: -6.201675-0.002100j, Rel_err(%): 3.0384
[2025-06-05 12:04:27] Iter: 79/20000, R0[78/100], Temp: 0.1147, Energy: -6.206069-0.001621j, Rel_err(%): 2.9696
[2025-06-05 12:04:30] Iter: 80/20000, R0[79/100], Temp: 0.1049, Energy: -6.206692-0.000344j, Rel_err(%): 2.9598
[2025-06-05 12:04:33] Iter: 81/20000, R0[80/100], Temp: 0.0955, Energy: -6.203912-0.001213j, Rel_err(%): 3.0033
[2025-06-05 12:04:36] Iter: 82/20000, R0[81/100], Temp: 0.0865, Energy: -6.211741-0.002684j, Rel_err(%): 2.8812
[2025-06-05 12:04:39] Iter: 83/20000, R0[82/100], Temp: 0.0778, Energy: -6.202502-0.004535j, Rel_err(%): 3.0261
[2025-06-05 12:04:42] Iter: 84/20000, R0[83/100], Temp: 0.0696, Energy: -6.207224-0.001016j, Rel_err(%): 2.9515
[2025-06-05 12:04:45] Iter: 85/20000, R0[84/100], Temp: 0.0618, Energy: -6.205429+0.001654j, Rel_err(%): 2.9796
[2025-06-05 12:04:49] Iter: 86/20000, R0[85/100], Temp: 0.0545, Energy: -6.210265+0.003911j, Rel_err(%): 2.9046
[2025-06-05 12:04:52] Iter: 87/20000, R0[86/100], Temp: 0.0476, Energy: -6.215243+0.000860j, Rel_err(%): 2.8261
[2025-06-05 12:04:55] Iter: 88/20000, R0[87/100], Temp: 0.0411, Energy: -6.212624+0.001147j, Rel_err(%): 2.8671
[2025-06-05 12:04:58] Iter: 89/20000, R0[88/100], Temp: 0.0351, Energy: -6.213155+0.001005j, Rel_err(%): 2.8588
[2025-06-05 12:05:01] Iter: 90/20000, R0[89/100], Temp: 0.0296, Energy: -6.213989+0.000115j, Rel_err(%): 2.8457
[2025-06-05 12:05:04] Iter: 91/20000, R0[90/100], Temp: 0.0245, Energy: -6.218709-0.002280j, Rel_err(%): 2.7721
[2025-06-05 12:05:08] Iter: 92/20000, R0[91/100], Temp: 0.0199, Energy: -6.214153+0.001270j, Rel_err(%): 2.8432
[2025-06-05 12:05:11] Iter: 93/20000, R0[92/100], Temp: 0.0157, Energy: -6.219603-0.000853j, Rel_err(%): 2.7580
[2025-06-05 12:05:14] Iter: 94/20000, R0[93/100], Temp: 0.0120, Energy: -6.224557-0.002145j, Rel_err(%): 2.6807
[2025-06-05 12:05:17] Iter: 95/20000, R0[94/100], Temp: 0.0089, Energy: -6.222869-0.001149j, Rel_err(%): 2.7069
[2025-06-05 12:05:20] Iter: 96/20000, R0[95/100], Temp: 0.0062, Energy: -6.224158-0.002301j, Rel_err(%): 2.6870
[2025-06-05 12:05:23] Iter: 97/20000, R0[96/100], Temp: 0.0039, Energy: -6.224610+0.000394j, Rel_err(%): 2.6797
[2025-06-05 12:05:26] Iter: 98/20000, R0[97/100], Temp: 0.0022, Energy: -6.223696-0.005516j, Rel_err(%): 2.6953
[2025-06-05 12:05:29] Iter: 99/20000, R0[98/100], Temp: 0.0010, Energy: -6.227372-0.001939j, Rel_err(%): 2.6366
[2025-06-05 12:05:33] Iter: 100/20000, R0[99/100], Temp: 0.0002, Energy: -6.220604+0.001266j, Rel_err(%): 2.7424
[2025-06-05 12:05:33] RESTART #1 at iteration 101, Temperature reset from 0.0002 to 1.0000, Next period: 200 iterations
[2025-06-05 12:05:36] Iter: 101/20000, R1[0/200], Temp: 1.0000, Energy: -6.227902-0.000466j, Rel_err(%): 2.6282
[2025-06-05 12:05:39] Iter: 102/20000, R1[1/200], Temp: 0.9999, Energy: -6.229690-0.001103j, Rel_err(%): 2.6003
[2025-06-05 12:05:42] Iter: 103/20000, R1[2/200], Temp: 0.9998, Energy: -6.229216+0.000320j, Rel_err(%): 2.6076
[2025-06-05 12:05:45] Iter: 104/20000, R1[3/200], Temp: 0.9994, Energy: -6.234954-0.001549j, Rel_err(%): 2.5180
[2025-06-05 12:05:48] Iter: 105/20000, R1[4/200], Temp: 0.9990, Energy: -6.228245-0.000334j, Rel_err(%): 2.6228
[2025-06-05 12:05:51] Iter: 106/20000, R1[5/200], Temp: 0.9985, Energy: -6.229885-0.001998j, Rel_err(%): 2.5974
[2025-06-05 12:05:55] Iter: 107/20000, R1[6/200], Temp: 0.9978, Energy: -6.234075-0.000885j, Rel_err(%): 2.5317
[2025-06-05 12:05:58] Iter: 108/20000, R1[7/200], Temp: 0.9970, Energy: -6.232052-0.001071j, Rel_err(%): 2.5633
[2025-06-05 12:06:01] Iter: 109/20000, R1[8/200], Temp: 0.9961, Energy: -6.231190-0.000258j, Rel_err(%): 2.5768
[2025-06-05 12:06:04] Iter: 110/20000, R1[9/200], Temp: 0.9950, Energy: -6.230775-0.000147j, Rel_err(%): 2.5833
[2025-06-05 12:06:07] Iter: 111/20000, R1[10/200], Temp: 0.9938, Energy: -6.232810-0.001305j, Rel_err(%): 2.5515
[2025-06-05 12:06:10] Iter: 112/20000, R1[11/200], Temp: 0.9926, Energy: -6.236154+0.003336j, Rel_err(%): 2.4997
[2025-06-05 12:06:13] Iter: 113/20000, R1[12/200], Temp: 0.9911, Energy: -6.235743+0.000471j, Rel_err(%): 2.5056
[2025-06-05 12:06:17] Iter: 114/20000, R1[13/200], Temp: 0.9896, Energy: -6.239546+0.002733j, Rel_err(%): 2.4465
[2025-06-05 12:06:20] Iter: 115/20000, R1[14/200], Temp: 0.9880, Energy: -6.236742-0.000107j, Rel_err(%): 2.4900
[2025-06-05 12:06:23] Iter: 116/20000, R1[15/200], Temp: 0.9862, Energy: -6.236652+0.002634j, Rel_err(%): 2.4917
[2025-06-05 12:06:26] Iter: 117/20000, R1[16/200], Temp: 0.9843, Energy: -6.242562+0.001646j, Rel_err(%): 2.3991
[2025-06-05 12:06:29] Iter: 118/20000, R1[17/200], Temp: 0.9823, Energy: -6.242064-0.001521j, Rel_err(%): 2.4069
[2025-06-05 12:06:32] Iter: 119/20000, R1[18/200], Temp: 0.9801, Energy: -6.240669-0.000981j, Rel_err(%): 2.4286
[2025-06-05 12:06:35] Iter: 120/20000, R1[19/200], Temp: 0.9779, Energy: -6.240525-0.002827j, Rel_err(%): 2.4312
[2025-06-05 12:06:39] Iter: 121/20000, R1[20/200], Temp: 0.9755, Energy: -6.243677-0.000002j, Rel_err(%): 2.3815
[2025-06-05 12:06:42] Iter: 122/20000, R1[21/200], Temp: 0.9730, Energy: -6.247867+0.000572j, Rel_err(%): 2.3160
[2025-06-05 12:06:45] Iter: 123/20000, R1[22/200], Temp: 0.9704, Energy: -6.244164+0.001717j, Rel_err(%): 2.3741
[2025-06-05 12:06:48] Iter: 124/20000, R1[23/200], Temp: 0.9677, Energy: -6.242444+0.002057j, Rel_err(%): 2.4010
[2025-06-05 12:06:51] Iter: 125/20000, R1[24/200], Temp: 0.9649, Energy: -6.247142+0.003784j, Rel_err(%): 2.3281
[2025-06-05 12:06:54] Iter: 126/20000, R1[25/200], Temp: 0.9619, Energy: -6.243767+0.001202j, Rel_err(%): 2.3802
[2025-06-05 12:06:57] Iter: 127/20000, R1[26/200], Temp: 0.9589, Energy: -6.243432+0.000208j, Rel_err(%): 2.3854
[2025-06-05 12:07:01] Iter: 128/20000, R1[27/200], Temp: 0.9557, Energy: -6.245678+0.000143j, Rel_err(%): 2.3502
[2025-06-05 12:07:04] Iter: 129/20000, R1[28/200], Temp: 0.9524, Energy: -6.245491+0.000652j, Rel_err(%): 2.3532
[2025-06-05 12:07:07] Iter: 130/20000, R1[29/200], Temp: 0.9490, Energy: -6.244117-0.002106j, Rel_err(%): 2.3749
[2025-06-05 12:07:10] Iter: 131/20000, R1[30/200], Temp: 0.9455, Energy: -6.246833-0.001255j, Rel_err(%): 2.3323
[2025-06-05 12:07:13] Iter: 132/20000, R1[31/200], Temp: 0.9419, Energy: -6.246918+0.000602j, Rel_err(%): 2.3309
[2025-06-05 12:07:16] Iter: 133/20000, R1[32/200], Temp: 0.9382, Energy: -6.249840+0.002902j, Rel_err(%): 2.2856
[2025-06-05 12:07:19] Iter: 134/20000, R1[33/200], Temp: 0.9343, Energy: -6.246163-0.000918j, Rel_err(%): 2.3427
[2025-06-05 12:07:23] Iter: 135/20000, R1[34/200], Temp: 0.9304, Energy: -6.250221+0.001331j, Rel_err(%): 2.2793
[2025-06-05 12:07:26] Iter: 136/20000, R1[35/200], Temp: 0.9263, Energy: -6.247563-0.000634j, Rel_err(%): 2.3208
[2025-06-05 12:07:29] Iter: 137/20000, R1[36/200], Temp: 0.9222, Energy: -6.251884-0.001319j, Rel_err(%): 2.2533
[2025-06-05 12:07:32] Iter: 138/20000, R1[37/200], Temp: 0.9179, Energy: -6.250122+0.001856j, Rel_err(%): 2.2810
[2025-06-05 12:07:35] Iter: 139/20000, R1[38/200], Temp: 0.9135, Energy: -6.250443+0.001305j, Rel_err(%): 2.2758
[2025-06-05 12:07:38] Iter: 140/20000, R1[39/200], Temp: 0.9091, Energy: -6.250795-0.003875j, Rel_err(%): 2.2711
[2025-06-05 12:07:41] Iter: 141/20000, R1[40/200], Temp: 0.9045, Energy: -6.253808+0.003307j, Rel_err(%): 2.2237
[2025-06-05 12:07:45] Iter: 142/20000, R1[41/200], Temp: 0.8998, Energy: -6.255238-0.000294j, Rel_err(%): 2.2008
[2025-06-05 12:07:48] Iter: 143/20000, R1[42/200], Temp: 0.8951, Energy: -6.249726+0.003181j, Rel_err(%): 2.2875
[2025-06-05 12:07:51] Iter: 144/20000, R1[43/200], Temp: 0.8902, Energy: -6.253323-0.001809j, Rel_err(%): 2.2309
[2025-06-05 12:07:54] Iter: 145/20000, R1[44/200], Temp: 0.8853, Energy: -6.252958-0.001685j, Rel_err(%): 2.2366
[2025-06-05 12:07:57] Iter: 146/20000, R1[45/200], Temp: 0.8802, Energy: -6.255654+0.000427j, Rel_err(%): 2.1943
[2025-06-05 12:08:00] Iter: 147/20000, R1[46/200], Temp: 0.8751, Energy: -6.256759-0.001614j, Rel_err(%): 2.1771
[2025-06-05 12:08:03] Iter: 148/20000, R1[47/200], Temp: 0.8698, Energy: -6.252806-0.001329j, Rel_err(%): 2.2389
[2025-06-05 12:08:07] Iter: 149/20000, R1[48/200], Temp: 0.8645, Energy: -6.255863+0.000504j, Rel_err(%): 2.1910
[2025-06-05 12:08:10] Iter: 150/20000, R1[49/200], Temp: 0.8591, Energy: -6.257686-0.001776j, Rel_err(%): 2.1627
[2025-06-05 12:08:13] Iter: 151/20000, R1[50/200], Temp: 0.8536, Energy: -6.254137+0.002837j, Rel_err(%): 2.2184
[2025-06-05 12:08:16] Iter: 152/20000, R1[51/200], Temp: 0.8480, Energy: -6.255887+0.001923j, Rel_err(%): 2.1908
[2025-06-05 12:08:19] Iter: 153/20000, R1[52/200], Temp: 0.8423, Energy: -6.255794+0.000540j, Rel_err(%): 2.1921
[2025-06-05 12:08:22] Iter: 154/20000, R1[53/200], Temp: 0.8365, Energy: -6.254439-0.000442j, Rel_err(%): 2.2133
[2025-06-05 12:08:25] Iter: 155/20000, R1[54/200], Temp: 0.8307, Energy: -6.255619+0.000475j, Rel_err(%): 2.1948
[2025-06-05 12:08:29] Iter: 156/20000, R1[55/200], Temp: 0.8247, Energy: -6.254599-0.001381j, Rel_err(%): 2.2109
[2025-06-05 12:08:32] Iter: 157/20000, R1[56/200], Temp: 0.8187, Energy: -6.255545-0.003005j, Rel_err(%): 2.1965
[2025-06-05 12:08:35] Iter: 158/20000, R1[57/200], Temp: 0.8126, Energy: -6.255932+0.001638j, Rel_err(%): 2.1901
[2025-06-05 12:08:38] Iter: 159/20000, R1[58/200], Temp: 0.8065, Energy: -6.254051-0.001726j, Rel_err(%): 2.2195
[2025-06-05 12:08:41] Iter: 160/20000, R1[59/200], Temp: 0.8002, Energy: -6.255780-0.000232j, Rel_err(%): 2.1923
[2025-06-05 12:08:44] Iter: 161/20000, R1[60/200], Temp: 0.7939, Energy: -6.259110+0.000053j, Rel_err(%): 2.1402
[2025-06-05 12:08:47] Iter: 162/20000, R1[61/200], Temp: 0.7875, Energy: -6.255207-0.000129j, Rel_err(%): 2.2013
[2025-06-05 12:08:51] Iter: 163/20000, R1[62/200], Temp: 0.7810, Energy: -6.258007-0.000864j, Rel_err(%): 2.1575
[2025-06-05 12:08:54] Iter: 164/20000, R1[63/200], Temp: 0.7745, Energy: -6.258051-0.000327j, Rel_err(%): 2.1568
[2025-06-05 12:08:57] Iter: 165/20000, R1[64/200], Temp: 0.7679, Energy: -6.257880+0.000842j, Rel_err(%): 2.1595
[2025-06-05 12:09:00] Iter: 166/20000, R1[65/200], Temp: 0.7612, Energy: -6.257127-0.003495j, Rel_err(%): 2.1719
[2025-06-05 12:09:03] Iter: 167/20000, R1[66/200], Temp: 0.7545, Energy: -6.256749-0.001646j, Rel_err(%): 2.1773
[2025-06-05 12:09:06] Iter: 168/20000, R1[67/200], Temp: 0.7477, Energy: -6.256806+0.000835j, Rel_err(%): 2.1763
[2025-06-05 12:09:09] Iter: 169/20000, R1[68/200], Temp: 0.7409, Energy: -6.257420-0.000991j, Rel_err(%): 2.1667
[2025-06-05 12:09:13] Iter: 170/20000, R1[69/200], Temp: 0.7340, Energy: -6.256997-0.000834j, Rel_err(%): 2.1733
[2025-06-05 12:09:16] Iter: 171/20000, R1[70/200], Temp: 0.7270, Energy: -6.260129+0.001557j, Rel_err(%): 2.1245
[2025-06-05 12:09:19] Iter: 172/20000, R1[71/200], Temp: 0.7200, Energy: -6.259931-0.001205j, Rel_err(%): 2.1275
[2025-06-05 12:09:22] Iter: 173/20000, R1[72/200], Temp: 0.7129, Energy: -6.260769+0.001231j, Rel_err(%): 2.1144
[2025-06-05 12:09:25] Iter: 174/20000, R1[73/200], Temp: 0.7058, Energy: -6.261715-0.000135j, Rel_err(%): 2.0995
[2025-06-05 12:09:28] Iter: 175/20000, R1[74/200], Temp: 0.6986, Energy: -6.258143-0.001088j, Rel_err(%): 2.1554
[2025-06-05 12:09:31] Iter: 176/20000, R1[75/200], Temp: 0.6913, Energy: -6.259516+0.000539j, Rel_err(%): 2.1339
[2025-06-05 12:09:34] Iter: 177/20000, R1[76/200], Temp: 0.6841, Energy: -6.260521+0.004604j, Rel_err(%): 2.1194
[2025-06-05 12:09:38] Iter: 178/20000, R1[77/200], Temp: 0.6767, Energy: -6.260135-0.001734j, Rel_err(%): 2.1244
[2025-06-05 12:09:41] Iter: 179/20000, R1[78/200], Temp: 0.6694, Energy: -6.260735+0.002419j, Rel_err(%): 2.1152
[2025-06-05 12:09:44] Iter: 180/20000, R1[79/200], Temp: 0.6620, Energy: -6.258762-0.001791j, Rel_err(%): 2.1459
[2025-06-05 12:09:47] Iter: 181/20000, R1[80/200], Temp: 0.6545, Energy: -6.263215+0.000615j, Rel_err(%): 2.0761
[2025-06-05 12:09:50] Iter: 182/20000, R1[81/200], Temp: 0.6470, Energy: -6.260876+0.001415j, Rel_err(%): 2.1127
[2025-06-05 12:09:53] Iter: 183/20000, R1[82/200], Temp: 0.6395, Energy: -6.261768+0.000204j, Rel_err(%): 2.0987
[2025-06-05 12:09:57] Iter: 184/20000, R1[83/200], Temp: 0.6319, Energy: -6.261227+0.000902j, Rel_err(%): 2.1072
[2025-06-05 12:10:00] Iter: 185/20000, R1[84/200], Temp: 0.6243, Energy: -6.260615+0.001968j, Rel_err(%): 2.1169
[2025-06-05 12:10:03] Iter: 186/20000, R1[85/200], Temp: 0.6167, Energy: -6.260389+0.000624j, Rel_err(%): 2.1203
[2025-06-05 12:10:06] Iter: 187/20000, R1[86/200], Temp: 0.6091, Energy: -6.262008-0.001493j, Rel_err(%): 2.0951
[2025-06-05 12:10:09] Iter: 188/20000, R1[87/200], Temp: 0.6014, Energy: -6.264397+0.000773j, Rel_err(%): 2.0576
[2025-06-05 12:10:12] Iter: 189/20000, R1[88/200], Temp: 0.5937, Energy: -6.263769+0.002108j, Rel_err(%): 2.0677
[2025-06-05 12:10:16] Iter: 190/20000, R1[89/200], Temp: 0.5860, Energy: -6.261664+0.001215j, Rel_err(%): 2.1004
[2025-06-05 12:10:19] Iter: 191/20000, R1[90/200], Temp: 0.5782, Energy: -6.263122-0.000140j, Rel_err(%): 2.0775
[2025-06-05 12:10:22] Iter: 192/20000, R1[91/200], Temp: 0.5705, Energy: -6.264797+0.000126j, Rel_err(%): 2.0513
[2025-06-05 12:10:25] Iter: 193/20000, R1[92/200], Temp: 0.5627, Energy: -6.263420+0.002070j, Rel_err(%): 2.0731
[2025-06-05 12:10:28] Iter: 194/20000, R1[93/200], Temp: 0.5549, Energy: -6.265271-0.000589j, Rel_err(%): 2.0439
[2025-06-05 12:10:31] Iter: 195/20000, R1[94/200], Temp: 0.5471, Energy: -6.260556+0.000601j, Rel_err(%): 2.1177
[2025-06-05 12:10:34] Iter: 196/20000, R1[95/200], Temp: 0.5392, Energy: -6.266961-0.000192j, Rel_err(%): 2.0175
[2025-06-05 12:10:38] Iter: 197/20000, R1[96/200], Temp: 0.5314, Energy: -6.266531-0.001253j, Rel_err(%): 2.0243
[2025-06-05 12:10:41] Iter: 198/20000, R1[97/200], Temp: 0.5236, Energy: -6.263704-0.001213j, Rel_err(%): 2.0685
[2025-06-05 12:10:44] Iter: 199/20000, R1[98/200], Temp: 0.5157, Energy: -6.262882-0.000873j, Rel_err(%): 2.0813
[2025-06-05 12:10:47] Iter: 200/20000, R1[99/200], Temp: 0.5079, Energy: -6.261875+0.001825j, Rel_err(%): 2.0972
[2025-06-05 12:10:50] Iter: 201/20000, R1[100/200], Temp: 0.5000, Energy: -6.258865-0.000687j, Rel_err(%): 2.1441
[2025-06-05 12:10:54] Iter: 202/20000, R1[101/200], Temp: 0.4921, Energy: -6.262284+0.000823j, Rel_err(%): 2.0907
[2025-06-05 12:10:57] Iter: 203/20000, R1[102/200], Temp: 0.4843, Energy: -6.264554+0.000233j, Rel_err(%): 2.0551
[2025-06-05 12:11:00] Iter: 204/20000, R1[103/200], Temp: 0.4764, Energy: -6.266154-0.000016j, Rel_err(%): 2.0301
[2025-06-05 12:11:03] Iter: 205/20000, R1[104/200], Temp: 0.4686, Energy: -6.262594-0.001775j, Rel_err(%): 2.0860
[2025-06-05 12:11:06] Iter: 206/20000, R1[105/200], Temp: 0.4608, Energy: -6.265384+0.001495j, Rel_err(%): 2.0423
[2025-06-05 12:11:09] Iter: 207/20000, R1[106/200], Temp: 0.4529, Energy: -6.267295-0.001626j, Rel_err(%): 2.0124
[2025-06-05 12:11:12] Iter: 208/20000, R1[107/200], Temp: 0.4451, Energy: -6.264639-0.000216j, Rel_err(%): 2.0538
[2025-06-05 12:11:16] Iter: 209/20000, R1[108/200], Temp: 0.4373, Energy: -6.267592+0.000141j, Rel_err(%): 2.0076
[2025-06-05 12:11:19] Iter: 210/20000, R1[109/200], Temp: 0.4295, Energy: -6.265195-0.001132j, Rel_err(%): 2.0452
[2025-06-05 12:11:22] Iter: 211/20000, R1[110/200], Temp: 0.4218, Energy: -6.263090-0.002044j, Rel_err(%): 2.0783
[2025-06-05 12:11:25] Iter: 212/20000, R1[111/200], Temp: 0.4140, Energy: -6.264828+0.000398j, Rel_err(%): 2.0509
[2025-06-05 12:11:28] Iter: 213/20000, R1[112/200], Temp: 0.4063, Energy: -6.264405-0.001791j, Rel_err(%): 2.0577
[2025-06-05 12:11:31] Iter: 214/20000, R1[113/200], Temp: 0.3986, Energy: -6.265812+0.001005j, Rel_err(%): 2.0355
[2025-06-05 12:11:35] Iter: 215/20000, R1[114/200], Temp: 0.3909, Energy: -6.265665+0.002718j, Rel_err(%): 2.0382
[2025-06-05 12:11:38] Iter: 216/20000, R1[115/200], Temp: 0.3833, Energy: -6.264993+0.002460j, Rel_err(%): 2.0486
[2025-06-05 12:11:41] Iter: 217/20000, R1[116/200], Temp: 0.3757, Energy: -6.266541-0.000711j, Rel_err(%): 2.0241
[2025-06-05 12:11:44] Iter: 218/20000, R1[117/200], Temp: 0.3681, Energy: -6.267247-0.000680j, Rel_err(%): 2.0131
[2025-06-05 12:11:47] Iter: 219/20000, R1[118/200], Temp: 0.3605, Energy: -6.267514-0.000510j, Rel_err(%): 2.0089
[2025-06-05 12:11:50] Iter: 220/20000, R1[119/200], Temp: 0.3530, Energy: -6.264956-0.000351j, Rel_err(%): 2.0489
[2025-06-05 12:11:54] Iter: 221/20000, R1[120/200], Temp: 0.3455, Energy: -6.264471-0.001464j, Rel_err(%): 2.0566
[2025-06-05 12:11:57] Iter: 222/20000, R1[121/200], Temp: 0.3380, Energy: -6.264649+0.002167j, Rel_err(%): 2.0539
[2025-06-05 12:12:00] Iter: 223/20000, R1[122/200], Temp: 0.3306, Energy: -6.265754-0.001229j, Rel_err(%): 2.0365
[2025-06-05 12:12:03] Iter: 224/20000, R1[123/200], Temp: 0.3233, Energy: -6.264803-0.000224j, Rel_err(%): 2.0512
[2025-06-05 12:12:06] Iter: 225/20000, R1[124/200], Temp: 0.3159, Energy: -6.267199+0.001867j, Rel_err(%): 2.0140
[2025-06-05 12:12:09] Iter: 226/20000, R1[125/200], Temp: 0.3087, Energy: -6.264322-0.000476j, Rel_err(%): 2.0588
[2025-06-05 12:12:12] Iter: 227/20000, R1[126/200], Temp: 0.3014, Energy: -6.265352-0.001196j, Rel_err(%): 2.0427
[2025-06-05 12:12:16] Iter: 228/20000, R1[127/200], Temp: 0.2942, Energy: -6.266524-0.000889j, Rel_err(%): 2.0244
[2025-06-05 12:12:19] Iter: 229/20000, R1[128/200], Temp: 0.2871, Energy: -6.265147+0.000309j, Rel_err(%): 2.0459
[2025-06-05 12:12:22] Iter: 230/20000, R1[129/200], Temp: 0.2800, Energy: -6.267200-0.000623j, Rel_err(%): 2.0138
[2025-06-05 12:12:25] Iter: 231/20000, R1[130/200], Temp: 0.2730, Energy: -6.266506-0.001174j, Rel_err(%): 2.0247
[2025-06-05 12:12:28] Iter: 232/20000, R1[131/200], Temp: 0.2660, Energy: -6.266259-0.002352j, Rel_err(%): 2.0288
[2025-06-05 12:12:31] Iter: 233/20000, R1[132/200], Temp: 0.2591, Energy: -6.268577+0.000487j, Rel_err(%): 1.9922
[2025-06-05 12:12:34] Iter: 234/20000, R1[133/200], Temp: 0.2523, Energy: -6.267586+0.001138j, Rel_err(%): 2.0078
[2025-06-05 12:12:38] Iter: 235/20000, R1[134/200], Temp: 0.2455, Energy: -6.268986-0.000299j, Rel_err(%): 1.9858
[2025-06-05 12:12:41] Iter: 236/20000, R1[135/200], Temp: 0.2388, Energy: -6.264391-0.002138j, Rel_err(%): 2.0579
[2025-06-05 12:12:44] Iter: 237/20000, R1[136/200], Temp: 0.2321, Energy: -6.268027-0.002913j, Rel_err(%): 2.0013
[2025-06-05 12:12:47] Iter: 238/20000, R1[137/200], Temp: 0.2255, Energy: -6.266515+0.000731j, Rel_err(%): 2.0245
[2025-06-05 12:12:50] Iter: 239/20000, R1[138/200], Temp: 0.2190, Energy: -6.265172+0.003004j, Rel_err(%): 2.0460
[2025-06-05 12:12:53] Iter: 240/20000, R1[139/200], Temp: 0.2125, Energy: -6.268134-0.002901j, Rel_err(%): 1.9997
[2025-06-05 12:12:57] Iter: 241/20000, R1[140/200], Temp: 0.2061, Energy: -6.268418-0.000360j, Rel_err(%): 1.9947
[2025-06-05 12:13:00] Iter: 242/20000, R1[141/200], Temp: 0.1998, Energy: -6.268179+0.000355j, Rel_err(%): 1.9985
[2025-06-05 12:13:03] Iter: 243/20000, R1[142/200], Temp: 0.1935, Energy: -6.269000+0.001924j, Rel_err(%): 1.9858
[2025-06-05 12:13:06] Iter: 244/20000, R1[143/200], Temp: 0.1874, Energy: -6.266150+0.001910j, Rel_err(%): 2.0304
[2025-06-05 12:13:09] Iter: 245/20000, R1[144/200], Temp: 0.1813, Energy: -6.266159+0.002894j, Rel_err(%): 2.0305
[2025-06-05 12:13:12] Iter: 246/20000, R1[145/200], Temp: 0.1753, Energy: -6.264591+0.000166j, Rel_err(%): 2.0546
[2025-06-05 12:13:16] Iter: 247/20000, R1[146/200], Temp: 0.1693, Energy: -6.269194+0.000233j, Rel_err(%): 1.9826
[2025-06-05 12:13:19] Iter: 248/20000, R1[147/200], Temp: 0.1635, Energy: -6.268792-0.001685j, Rel_err(%): 1.9890
[2025-06-05 12:13:22] Iter: 249/20000, R1[148/200], Temp: 0.1577, Energy: -6.267983-0.000127j, Rel_err(%): 2.0015
[2025-06-05 12:13:25] Iter: 250/20000, R1[149/200], Temp: 0.1520, Energy: -6.266440-0.000678j, Rel_err(%): 2.0257
[2025-06-05 12:13:28] Iter: 251/20000, R1[150/200], Temp: 0.1464, Energy: -6.266978+0.001427j, Rel_err(%): 2.0174
[2025-06-05 12:13:31] Iter: 252/20000, R1[151/200], Temp: 0.1409, Energy: -6.266451-0.000879j, Rel_err(%): 2.0255
[2025-06-05 12:13:34] Iter: 253/20000, R1[152/200], Temp: 0.1355, Energy: -6.269716-0.001205j, Rel_err(%): 1.9745
[2025-06-05 12:13:38] Iter: 254/20000, R1[153/200], Temp: 0.1302, Energy: -6.266833-0.000909j, Rel_err(%): 2.0195
[2025-06-05 12:13:41] Iter: 255/20000, R1[154/200], Temp: 0.1249, Energy: -6.267755-0.001076j, Rel_err(%): 2.0052
[2025-06-05 12:13:44] Iter: 256/20000, R1[155/200], Temp: 0.1198, Energy: -6.267297-0.000662j, Rel_err(%): 2.0123
[2025-06-05 12:13:47] Iter: 257/20000, R1[156/200], Temp: 0.1147, Energy: -6.269669+0.001439j, Rel_err(%): 1.9753
[2025-06-05 12:13:50] Iter: 258/20000, R1[157/200], Temp: 0.1098, Energy: -6.269488+0.001575j, Rel_err(%): 1.9781
[2025-06-05 12:13:53] Iter: 259/20000, R1[158/200], Temp: 0.1049, Energy: -6.268832+0.001331j, Rel_err(%): 1.9884
[2025-06-05 12:13:57] Iter: 260/20000, R1[159/200], Temp: 0.1002, Energy: -6.268456+0.000986j, Rel_err(%): 1.9942
[2025-06-05 12:14:00] Iter: 261/20000, R1[160/200], Temp: 0.0955, Energy: -6.269443-0.000269j, Rel_err(%): 1.9787
[2025-06-05 12:14:03] Iter: 262/20000, R1[161/200], Temp: 0.0909, Energy: -6.270924+0.002071j, Rel_err(%): 1.9558
[2025-06-05 12:14:06] Iter: 263/20000, R1[162/200], Temp: 0.0865, Energy: -6.269007+0.000533j, Rel_err(%): 1.9855
[2025-06-05 12:14:09] Iter: 264/20000, R1[163/200], Temp: 0.0821, Energy: -6.265844+0.000458j, Rel_err(%): 2.0350
[2025-06-05 12:14:12] Iter: 265/20000, R1[164/200], Temp: 0.0778, Energy: -6.269181-0.001093j, Rel_err(%): 1.9829
[2025-06-05 12:14:16] Iter: 266/20000, R1[165/200], Temp: 0.0737, Energy: -6.268329+0.000751j, Rel_err(%): 1.9961
[2025-06-05 12:14:19] Iter: 267/20000, R1[166/200], Temp: 0.0696, Energy: -6.267404-0.001647j, Rel_err(%): 2.0107
[2025-06-05 12:14:22] Iter: 268/20000, R1[167/200], Temp: 0.0657, Energy: -6.265729-0.000008j, Rel_err(%): 2.0368
[2025-06-05 12:14:25] Iter: 269/20000, R1[168/200], Temp: 0.0618, Energy: -6.270904+0.000160j, Rel_err(%): 1.9558
[2025-06-05 12:14:28] Iter: 270/20000, R1[169/200], Temp: 0.0581, Energy: -6.267767-0.001469j, Rel_err(%): 2.0050
[2025-06-05 12:14:31] Iter: 271/20000, R1[170/200], Temp: 0.0545, Energy: -6.268872-0.000338j, Rel_err(%): 1.9876
[2025-06-05 12:14:34] Iter: 272/20000, R1[171/200], Temp: 0.0510, Energy: -6.266297+0.000446j, Rel_err(%): 2.0279
[2025-06-05 12:14:38] Iter: 273/20000, R1[172/200], Temp: 0.0476, Energy: -6.269862+0.000704j, Rel_err(%): 1.9722
[2025-06-05 12:14:41] Iter: 274/20000, R1[173/200], Temp: 0.0443, Energy: -6.269080+0.001153j, Rel_err(%): 1.9844
[2025-06-05 12:14:44] Iter: 275/20000, R1[174/200], Temp: 0.0411, Energy: -6.268091+0.002404j, Rel_err(%): 2.0002
[2025-06-05 12:14:47] Iter: 276/20000, R1[175/200], Temp: 0.0381, Energy: -6.270827-0.000058j, Rel_err(%): 1.9571
[2025-06-05 12:14:50] Iter: 277/20000, R1[176/200], Temp: 0.0351, Energy: -6.269202-0.000284j, Rel_err(%): 1.9825
[2025-06-05 12:14:53] Iter: 278/20000, R1[177/200], Temp: 0.0323, Energy: -6.267981+0.001203j, Rel_err(%): 2.0016
[2025-06-05 12:14:57] Iter: 279/20000, R1[178/200], Temp: 0.0296, Energy: -6.271013-0.001522j, Rel_err(%): 1.9543
[2025-06-05 12:15:00] Iter: 280/20000, R1[179/200], Temp: 0.0270, Energy: -6.270779-0.001048j, Rel_err(%): 1.9579
[2025-06-05 12:15:03] Iter: 281/20000, R1[180/200], Temp: 0.0245, Energy: -6.271099+0.000601j, Rel_err(%): 1.9528
[2025-06-05 12:15:06] Iter: 282/20000, R1[181/200], Temp: 0.0221, Energy: -6.267489+0.002708j, Rel_err(%): 2.0097
[2025-06-05 12:15:09] Iter: 283/20000, R1[182/200], Temp: 0.0199, Energy: -6.269252-0.000869j, Rel_err(%): 1.9817
[2025-06-05 12:15:12] Iter: 284/20000, R1[183/200], Temp: 0.0177, Energy: -6.272057-0.000210j, Rel_err(%): 1.9378
[2025-06-05 12:15:16] Iter: 285/20000, R1[184/200], Temp: 0.0157, Energy: -6.269113-0.001954j, Rel_err(%): 1.9841
[2025-06-05 12:15:19] Iter: 286/20000, R1[185/200], Temp: 0.0138, Energy: -6.268798+0.000853j, Rel_err(%): 1.9888
[2025-06-05 12:15:22] Iter: 287/20000, R1[186/200], Temp: 0.0120, Energy: -6.270943-0.000226j, Rel_err(%): 1.9552
[2025-06-05 12:15:25] Iter: 288/20000, R1[187/200], Temp: 0.0104, Energy: -6.272271+0.000458j, Rel_err(%): 1.9345
[2025-06-05 12:15:28] Iter: 289/20000, R1[188/200], Temp: 0.0089, Energy: -6.269659-0.000439j, Rel_err(%): 1.9753
[2025-06-05 12:15:31] Iter: 290/20000, R1[189/200], Temp: 0.0074, Energy: -6.270536+0.002307j, Rel_err(%): 1.9619
[2025-06-05 12:15:34] Iter: 291/20000, R1[190/200], Temp: 0.0062, Energy: -6.270006-0.000510j, Rel_err(%): 1.9699
[2025-06-05 12:15:38] Iter: 292/20000, R1[191/200], Temp: 0.0050, Energy: -6.271168-0.001773j, Rel_err(%): 1.9519
[2025-06-05 12:15:41] Iter: 293/20000, R1[192/200], Temp: 0.0039, Energy: -6.270160-0.000062j, Rel_err(%): 1.9675
[2025-06-05 12:15:44] Iter: 294/20000, R1[193/200], Temp: 0.0030, Energy: -6.268055-0.002302j, Rel_err(%): 2.0007
[2025-06-05 12:15:47] Iter: 295/20000, R1[194/200], Temp: 0.0022, Energy: -6.268931+0.001398j, Rel_err(%): 1.9868
[2025-06-05 12:15:50] Iter: 296/20000, R1[195/200], Temp: 0.0015, Energy: -6.270025-0.001786j, Rel_err(%): 1.9698
[2025-06-05 12:15:53] Iter: 297/20000, R1[196/200], Temp: 0.0010, Energy: -6.269983-0.000478j, Rel_err(%): 1.9703
[2025-06-05 12:15:57] Iter: 298/20000, R1[197/200], Temp: 0.0006, Energy: -6.271636-0.001596j, Rel_err(%): 1.9446
[2025-06-05 12:16:00] Iter: 299/20000, R1[198/200], Temp: 0.0002, Energy: -6.270692-0.000033j, Rel_err(%): 1.9592
[2025-06-05 12:16:03] Iter: 300/20000, R1[199/200], Temp: 0.0001, Energy: -6.269963-0.002984j, Rel_err(%): 1.9711
[2025-06-05 12:16:03] RESTART #2 at iteration 301, Temperature reset from 0.0001 to 1.0000, Next period: 400 iterations
[2025-06-05 12:16:06] Iter: 301/20000, R2[0/400], Temp: 1.0000, Energy: -6.271949+0.000173j, Rel_err(%): 1.9395
[2025-06-05 12:16:09] Iter: 302/20000, R2[1/400], Temp: 1.0000, Energy: -6.269370+0.000614j, Rel_err(%): 1.9799
[2025-06-05 12:16:12] Iter: 303/20000, R2[2/400], Temp: 0.9999, Energy: -6.268364-0.000849j, Rel_err(%): 1.9956
[2025-06-05 12:16:16] Iter: 304/20000, R2[3/400], Temp: 0.9999, Energy: -6.269757+0.001450j, Rel_err(%): 1.9739
[2025-06-05 12:16:19] Iter: 305/20000, R2[4/400], Temp: 0.9998, Energy: -6.269616-0.001481j, Rel_err(%): 1.9761
[2025-06-05 12:16:22] Iter: 306/20000, R2[5/400], Temp: 0.9996, Energy: -6.269835-0.000540j, Rel_err(%): 1.9726
[2025-06-05 12:16:25] Iter: 307/20000, R2[6/400], Temp: 0.9994, Energy: -6.269542-0.000941j, Rel_err(%): 1.9772
[2025-06-05 12:16:28] Iter: 308/20000, R2[7/400], Temp: 0.9992, Energy: -6.268294+0.001287j, Rel_err(%): 1.9968
[2025-06-05 12:16:31] Iter: 309/20000, R2[8/400], Temp: 0.9990, Energy: -6.269208-0.000628j, Rel_err(%): 1.9824
[2025-06-05 12:16:34] Iter: 310/20000, R2[9/400], Temp: 0.9988, Energy: -6.269360+0.000935j, Rel_err(%): 1.9800
[2025-06-05 12:16:38] Iter: 311/20000, R2[10/400], Temp: 0.9985, Energy: -6.272021+0.002459j, Rel_err(%): 1.9388
[2025-06-05 12:16:41] Iter: 312/20000, R2[11/400], Temp: 0.9981, Energy: -6.271139+0.000465j, Rel_err(%): 1.9522
[2025-06-05 12:16:44] Iter: 313/20000, R2[12/400], Temp: 0.9978, Energy: -6.273753+0.001601j, Rel_err(%): 1.9115
[2025-06-05 12:16:47] Iter: 314/20000, R2[13/400], Temp: 0.9974, Energy: -6.272004+0.000354j, Rel_err(%): 1.9387
[2025-06-05 12:16:50] Iter: 315/20000, R2[14/400], Temp: 0.9970, Energy: -6.268590-0.000407j, Rel_err(%): 1.9920
[2025-06-05 12:16:53] Iter: 316/20000, R2[15/400], Temp: 0.9965, Energy: -6.267753+0.000570j, Rel_err(%): 2.0051
[2025-06-05 12:16:57] Iter: 317/20000, R2[16/400], Temp: 0.9961, Energy: -6.273233-0.001502j, Rel_err(%): 1.9196
[2025-06-05 12:17:00] Iter: 318/20000, R2[17/400], Temp: 0.9955, Energy: -6.270269-0.001431j, Rel_err(%): 1.9659
[2025-06-05 12:17:03] Iter: 319/20000, R2[18/400], Temp: 0.9950, Energy: -6.271078-0.001722j, Rel_err(%): 1.9533
[2025-06-05 12:17:06] Iter: 320/20000, R2[19/400], Temp: 0.9944, Energy: -6.271196+0.000946j, Rel_err(%): 1.9513
[2025-06-05 12:17:09] Iter: 321/20000, R2[20/400], Temp: 0.9938, Energy: -6.269735+0.000106j, Rel_err(%): 1.9741
[2025-06-05 12:17:12] Iter: 322/20000, R2[21/400], Temp: 0.9932, Energy: -6.271406-0.000949j, Rel_err(%): 1.9481
[2025-06-05 12:17:16] Iter: 323/20000, R2[22/400], Temp: 0.9926, Energy: -6.270497-0.000717j, Rel_err(%): 1.9622
[2025-06-05 12:17:19] Iter: 324/20000, R2[23/400], Temp: 0.9919, Energy: -6.271116-0.000378j, Rel_err(%): 1.9525
[2025-06-05 12:17:22] Iter: 325/20000, R2[24/400], Temp: 0.9911, Energy: -6.270895+0.001086j, Rel_err(%): 1.9561
[2025-06-05 12:17:25] Iter: 326/20000, R2[25/400], Temp: 0.9904, Energy: -6.272070-0.002911j, Rel_err(%): 1.9382
[2025-06-05 12:17:28] Iter: 327/20000, R2[26/400], Temp: 0.9896, Energy: -6.270850+0.001133j, Rel_err(%): 1.9568
[2025-06-05 12:17:31] Iter: 328/20000, R2[27/400], Temp: 0.9888, Energy: -6.269105-0.001511j, Rel_err(%): 1.9841
[2025-06-05 12:17:34] Iter: 329/20000, R2[28/400], Temp: 0.9880, Energy: -6.270731+0.000974j, Rel_err(%): 1.9586
[2025-06-05 12:17:38] Iter: 330/20000, R2[29/400], Temp: 0.9871, Energy: -6.273577+0.000970j, Rel_err(%): 1.9141
[2025-06-05 12:17:41] Iter: 331/20000, R2[30/400], Temp: 0.9862, Energy: -6.271879-0.002292j, Rel_err(%): 1.9409
[2025-06-05 12:17:44] Iter: 332/20000, R2[31/400], Temp: 0.9853, Energy: -6.275327-0.002936j, Rel_err(%): 1.8873
[2025-06-05 12:17:47] Iter: 333/20000, R2[32/400], Temp: 0.9843, Energy: -6.274466-0.000191j, Rel_err(%): 1.9002
[2025-06-05 12:17:50] Iter: 334/20000, R2[33/400], Temp: 0.9833, Energy: -6.272513-0.002046j, Rel_err(%): 1.9310
[2025-06-05 12:17:53] Iter: 335/20000, R2[34/400], Temp: 0.9823, Energy: -6.273365-0.001238j, Rel_err(%): 1.9175
[2025-06-05 12:17:57] Iter: 336/20000, R2[35/400], Temp: 0.9812, Energy: -6.272661-0.000891j, Rel_err(%): 1.9284
[2025-06-05 12:18:00] Iter: 337/20000, R2[36/400], Temp: 0.9801, Energy: -6.274138-0.000926j, Rel_err(%): 1.9053
[2025-06-05 12:18:03] Iter: 338/20000, R2[37/400], Temp: 0.9790, Energy: -6.271499-0.000260j, Rel_err(%): 1.9465
[2025-06-05 12:18:06] Iter: 339/20000, R2[38/400], Temp: 0.9779, Energy: -6.269803+0.001057j, Rel_err(%): 1.9731
[2025-06-05 12:18:09] Iter: 340/20000, R2[39/400], Temp: 0.9767, Energy: -6.272172-0.000409j, Rel_err(%): 1.9360
[2025-06-05 12:18:12] Iter: 341/20000, R2[40/400], Temp: 0.9755, Energy: -6.271214+0.000983j, Rel_err(%): 1.9511
[2025-06-05 12:18:16] Iter: 342/20000, R2[41/400], Temp: 0.9743, Energy: -6.271093-0.000346j, Rel_err(%): 1.9529
[2025-06-05 12:18:19] Iter: 343/20000, R2[42/400], Temp: 0.9730, Energy: -6.272169+0.000179j, Rel_err(%): 1.9361
[2025-06-05 12:18:22] Iter: 344/20000, R2[43/400], Temp: 0.9718, Energy: -6.270897-0.001055j, Rel_err(%): 1.9560
[2025-06-05 12:18:25] Iter: 345/20000, R2[44/400], Temp: 0.9704, Energy: -6.272890+0.000324j, Rel_err(%): 1.9248
[2025-06-05 12:18:28] Iter: 346/20000, R2[45/400], Temp: 0.9691, Energy: -6.271669-0.002410j, Rel_err(%): 1.9443
[2025-06-05 12:18:31] Iter: 347/20000, R2[46/400], Temp: 0.9677, Energy: -6.270897-0.001193j, Rel_err(%): 1.9560
[2025-06-05 12:18:34] Iter: 348/20000, R2[47/400], Temp: 0.9663, Energy: -6.270826-0.001321j, Rel_err(%): 1.9572
[2025-06-05 12:18:38] Iter: 349/20000, R2[48/400], Temp: 0.9649, Energy: -6.271906+0.000233j, Rel_err(%): 1.9402
[2025-06-05 12:18:41] Iter: 350/20000, R2[49/400], Temp: 0.9634, Energy: -6.272535-0.001551j, Rel_err(%): 1.9305
[2025-06-05 12:18:44] Iter: 351/20000, R2[50/400], Temp: 0.9619, Energy: -6.274300+0.000392j, Rel_err(%): 1.9028
[2025-06-05 12:18:47] Iter: 352/20000, R2[51/400], Temp: 0.9604, Energy: -6.273557+0.000500j, Rel_err(%): 1.9144
[2025-06-05 12:18:50] Iter: 353/20000, R2[52/400], Temp: 0.9589, Energy: -6.270923-0.002332j, Rel_err(%): 1.9559
[2025-06-05 12:18:53] Iter: 354/20000, R2[53/400], Temp: 0.9573, Energy: -6.273767+0.000508j, Rel_err(%): 1.9111
[2025-06-05 12:18:57] Iter: 355/20000, R2[54/400], Temp: 0.9557, Energy: -6.272627+0.001449j, Rel_err(%): 1.9290
[2025-06-05 12:19:00] Iter: 356/20000, R2[55/400], Temp: 0.9541, Energy: -6.272499-0.001694j, Rel_err(%): 1.9311
[2025-06-05 12:19:03] Iter: 357/20000, R2[56/400], Temp: 0.9524, Energy: -6.272413-0.001046j, Rel_err(%): 1.9323
[2025-06-05 12:19:06] Iter: 358/20000, R2[57/400], Temp: 0.9507, Energy: -6.269649+0.001120j, Rel_err(%): 1.9756
[2025-06-05 12:19:09] Iter: 359/20000, R2[58/400], Temp: 0.9490, Energy: -6.272730+0.000123j, Rel_err(%): 1.9273
[2025-06-05 12:19:12] Iter: 360/20000, R2[59/400], Temp: 0.9473, Energy: -6.275715-0.001200j, Rel_err(%): 1.8807
[2025-06-05 12:19:16] Iter: 361/20000, R2[60/400], Temp: 0.9455, Energy: -6.274120+0.000565j, Rel_err(%): 1.9056
[2025-06-05 12:19:19] Iter: 362/20000, R2[61/400], Temp: 0.9437, Energy: -6.271954+0.002637j, Rel_err(%): 1.9399
[2025-06-05 12:19:22] Iter: 363/20000, R2[62/400], Temp: 0.9419, Energy: -6.270514-0.000731j, Rel_err(%): 1.9620
[2025-06-05 12:19:25] Iter: 364/20000, R2[63/400], Temp: 0.9400, Energy: -6.275421+0.000183j, Rel_err(%): 1.8852
[2025-06-05 12:19:28] Iter: 365/20000, R2[64/400], Temp: 0.9382, Energy: -6.275474+0.000430j, Rel_err(%): 1.8844
[2025-06-05 12:19:31] Iter: 366/20000, R2[65/400], Temp: 0.9362, Energy: -6.273713+0.001150j, Rel_err(%): 1.9120
[2025-06-05 12:19:34] Iter: 367/20000, R2[66/400], Temp: 0.9343, Energy: -6.272147+0.000911j, Rel_err(%): 1.9365
[2025-06-05 12:19:38] Iter: 368/20000, R2[67/400], Temp: 0.9324, Energy: -6.273729+0.000782j, Rel_err(%): 1.9117
[2025-06-05 12:19:41] Iter: 369/20000, R2[68/400], Temp: 0.9304, Energy: -6.274807+0.000129j, Rel_err(%): 1.8948
[2025-06-05 12:19:44] Iter: 370/20000, R2[69/400], Temp: 0.9284, Energy: -6.272958-0.000028j, Rel_err(%): 1.9237
[2025-06-05 12:19:47] Iter: 371/20000, R2[70/400], Temp: 0.9263, Energy: -6.271435-0.001740j, Rel_err(%): 1.9477
[2025-06-05 12:19:50] Iter: 372/20000, R2[71/400], Temp: 0.9243, Energy: -6.272030+0.001077j, Rel_err(%): 1.9383
[2025-06-05 12:19:54] Iter: 373/20000, R2[72/400], Temp: 0.9222, Energy: -6.272604+0.001133j, Rel_err(%): 1.9293
[2025-06-05 12:19:57] Iter: 374/20000, R2[73/400], Temp: 0.9200, Energy: -6.271723+0.000607j, Rel_err(%): 1.9431
[2025-06-05 12:20:00] Iter: 375/20000, R2[74/400], Temp: 0.9179, Energy: -6.272054+0.001498j, Rel_err(%): 1.9380
[2025-06-05 12:20:03] Iter: 376/20000, R2[75/400], Temp: 0.9157, Energy: -6.272222-0.002458j, Rel_err(%): 1.9356
[2025-06-05 12:20:06] Iter: 377/20000, R2[76/400], Temp: 0.9135, Energy: -6.273183-0.000540j, Rel_err(%): 1.9202
[2025-06-05 12:20:09] Iter: 378/20000, R2[77/400], Temp: 0.9113, Energy: -6.272404-0.001970j, Rel_err(%): 1.9326
[2025-06-05 12:20:12] Iter: 379/20000, R2[78/400], Temp: 0.9091, Energy: -6.272205-0.000441j, Rel_err(%): 1.9355
[2025-06-05 12:20:16] Iter: 380/20000, R2[79/400], Temp: 0.9068, Energy: -6.273871-0.001312j, Rel_err(%): 1.9096
[2025-06-05 12:20:19] Iter: 381/20000, R2[80/400], Temp: 0.9045, Energy: -6.274566+0.001874j, Rel_err(%): 1.8988
[2025-06-05 12:20:22] Iter: 382/20000, R2[81/400], Temp: 0.9022, Energy: -6.271987+0.000869j, Rel_err(%): 1.9390
[2025-06-05 12:20:25] Iter: 383/20000, R2[82/400], Temp: 0.8998, Energy: -6.273772-0.000970j, Rel_err(%): 1.9111
[2025-06-05 12:20:28] Iter: 384/20000, R2[83/400], Temp: 0.8975, Energy: -6.273989+0.000955j, Rel_err(%): 1.9077
[2025-06-05 12:20:31] Iter: 385/20000, R2[84/400], Temp: 0.8951, Energy: -6.271613-0.001044j, Rel_err(%): 1.9448
[2025-06-05 12:20:35] Iter: 386/20000, R2[85/400], Temp: 0.8927, Energy: -6.272068-0.001617j, Rel_err(%): 1.9378
[2025-06-05 12:20:38] Iter: 387/20000, R2[86/400], Temp: 0.8902, Energy: -6.271526+0.000669j, Rel_err(%): 1.9461
[2025-06-05 12:20:41] Iter: 388/20000, R2[87/400], Temp: 0.8877, Energy: -6.272160-0.000245j, Rel_err(%): 1.9362
[2025-06-05 12:20:44] Iter: 389/20000, R2[88/400], Temp: 0.8853, Energy: -6.273509-0.001936j, Rel_err(%): 1.9154
[2025-06-05 12:20:47] Iter: 390/20000, R2[89/400], Temp: 0.8827, Energy: -6.271262-0.001719j, Rel_err(%): 1.9504
[2025-06-05 12:20:50] Iter: 391/20000, R2[90/400], Temp: 0.8802, Energy: -6.272574-0.002262j, Rel_err(%): 1.9301
[2025-06-05 12:20:53] Iter: 392/20000, R2[91/400], Temp: 0.8776, Energy: -6.272511-0.001047j, Rel_err(%): 1.9308
[2025-06-05 12:20:57] Iter: 393/20000, R2[92/400], Temp: 0.8751, Energy: -6.273106-0.001253j, Rel_err(%): 1.9215
[2025-06-05 12:21:00] Iter: 394/20000, R2[93/400], Temp: 0.8724, Energy: -6.273232-0.001618j, Rel_err(%): 1.9196
[2025-06-05 12:21:03] Iter: 395/20000, R2[94/400], Temp: 0.8698, Energy: -6.274927+0.000511j, Rel_err(%): 1.8930
[2025-06-05 12:21:06] Iter: 396/20000, R2[95/400], Temp: 0.8672, Energy: -6.273942-0.000205j, Rel_err(%): 1.9084
[2025-06-05 12:21:09] Iter: 397/20000, R2[96/400], Temp: 0.8645, Energy: -6.272189-0.000811j, Rel_err(%): 1.9358
[2025-06-05 12:21:12] Iter: 398/20000, R2[97/400], Temp: 0.8618, Energy: -6.274260+0.000029j, Rel_err(%): 1.9034
[2025-06-05 12:21:16] Iter: 399/20000, R2[98/400], Temp: 0.8591, Energy: -6.273886+0.000935j, Rel_err(%): 1.9093
[2025-06-05 12:21:19] Iter: 400/20000, R2[99/400], Temp: 0.8563, Energy: -6.272719-0.000695j, Rel_err(%): 1.9275
[2025-06-05 12:21:22] Iter: 401/20000, R2[100/400], Temp: 0.8536, Energy: -6.272642-0.000307j, Rel_err(%): 1.9287
[2025-06-05 12:21:25] Iter: 402/20000, R2[101/400], Temp: 0.8508, Energy: -6.272982-0.000108j, Rel_err(%): 1.9234
[2025-06-05 12:21:28] Iter: 403/20000, R2[102/400], Temp: 0.8480, Energy: -6.272501-0.000405j, Rel_err(%): 1.9309
[2025-06-05 12:21:31] Iter: 404/20000, R2[103/400], Temp: 0.8451, Energy: -6.272045+0.000023j, Rel_err(%): 1.9380
[2025-06-05 12:21:34] Iter: 405/20000, R2[104/400], Temp: 0.8423, Energy: -6.274605+0.000342j, Rel_err(%): 1.8980
[2025-06-05 12:21:38] Iter: 406/20000, R2[105/400], Temp: 0.8394, Energy: -6.275059-0.000345j, Rel_err(%): 1.8909
[2025-06-05 12:21:41] Iter: 407/20000, R2[106/400], Temp: 0.8365, Energy: -6.274725-0.000583j, Rel_err(%): 1.8961
[2025-06-05 12:21:44] Iter: 408/20000, R2[107/400], Temp: 0.8336, Energy: -6.276129+0.002364j, Rel_err(%): 1.8745
[2025-06-05 12:21:47] Iter: 409/20000, R2[108/400], Temp: 0.8307, Energy: -6.274949+0.001106j, Rel_err(%): 1.8927
[2025-06-05 12:21:50] Iter: 410/20000, R2[109/400], Temp: 0.8277, Energy: -6.272721+0.001011j, Rel_err(%): 1.9275
[2025-06-05 12:21:54] Iter: 411/20000, R2[110/400], Temp: 0.8247, Energy: -6.273035+0.000819j, Rel_err(%): 1.9226
[2025-06-05 12:21:57] Iter: 412/20000, R2[111/400], Temp: 0.8217, Energy: -6.271045+0.001357j, Rel_err(%): 1.9538
[2025-06-05 12:22:00] Iter: 413/20000, R2[112/400], Temp: 0.8187, Energy: -6.272380-0.000201j, Rel_err(%): 1.9328
[2025-06-05 12:22:03] Iter: 414/20000, R2[113/400], Temp: 0.8157, Energy: -6.274537-0.000250j, Rel_err(%): 1.8991
[2025-06-05 12:22:06] Iter: 415/20000, R2[114/400], Temp: 0.8126, Energy: -6.273021+0.000498j, Rel_err(%): 1.9228
[2025-06-05 12:22:09] Iter: 416/20000, R2[115/400], Temp: 0.8095, Energy: -6.274110+0.000882j, Rel_err(%): 1.9058
[2025-06-05 12:22:12] Iter: 417/20000, R2[116/400], Temp: 0.8065, Energy: -6.272609+0.000297j, Rel_err(%): 1.9292
[2025-06-05 12:22:16] Iter: 418/20000, R2[117/400], Temp: 0.8033, Energy: -6.271201+0.000431j, Rel_err(%): 1.9512
[2025-06-05 12:22:19] Iter: 419/20000, R2[118/400], Temp: 0.8002, Energy: -6.273216-0.001094j, Rel_err(%): 1.9198
[2025-06-05 12:22:22] Iter: 420/20000, R2[119/400], Temp: 0.7971, Energy: -6.276860-0.000318j, Rel_err(%): 1.8627
[2025-06-05 12:22:25] Iter: 421/20000, R2[120/400], Temp: 0.7939, Energy: -6.271397+0.000619j, Rel_err(%): 1.9482
[2025-06-05 12:22:28] Iter: 422/20000, R2[121/400], Temp: 0.7907, Energy: -6.271465-0.000758j, Rel_err(%): 1.9471
[2025-06-05 12:22:31] Iter: 423/20000, R2[122/400], Temp: 0.7875, Energy: -6.273153-0.001200j, Rel_err(%): 1.9208
[2025-06-05 12:22:34] Iter: 424/20000, R2[123/400], Temp: 0.7843, Energy: -6.275734+0.000039j, Rel_err(%): 1.8803
[2025-06-05 12:22:38] Iter: 425/20000, R2[124/400], Temp: 0.7810, Energy: -6.274423-0.001581j, Rel_err(%): 1.9010
[2025-06-05 12:22:41] Iter: 426/20000, R2[125/400], Temp: 0.7778, Energy: -6.273355-0.001142j, Rel_err(%): 1.9176
[2025-06-05 12:22:44] Iter: 427/20000, R2[126/400], Temp: 0.7745, Energy: -6.274050+0.000538j, Rel_err(%): 1.9067
[2025-06-05 12:22:47] Iter: 428/20000, R2[127/400], Temp: 0.7712, Energy: -6.273398-0.000717j, Rel_err(%): 1.9169
[2025-06-05 12:22:50] Iter: 429/20000, R2[128/400], Temp: 0.7679, Energy: -6.276033-0.000823j, Rel_err(%): 1.8757
[2025-06-05 12:22:53] Iter: 430/20000, R2[129/400], Temp: 0.7646, Energy: -6.275534+0.000949j, Rel_err(%): 1.8835
[2025-06-05 12:22:57] Iter: 431/20000, R2[130/400], Temp: 0.7612, Energy: -6.274179+0.000501j, Rel_err(%): 1.9047
[2025-06-05 12:23:00] Iter: 432/20000, R2[131/400], Temp: 0.7579, Energy: -6.275052+0.000856j, Rel_err(%): 1.8910
[2025-06-05 12:23:03] Iter: 433/20000, R2[132/400], Temp: 0.7545, Energy: -6.275156+0.000615j, Rel_err(%): 1.8894
[2025-06-05 12:23:06] Iter: 434/20000, R2[133/400], Temp: 0.7511, Energy: -6.272969-0.000379j, Rel_err(%): 1.9236
[2025-06-05 12:23:09] Iter: 435/20000, R2[134/400], Temp: 0.7477, Energy: -6.275638-0.000843j, Rel_err(%): 1.8819
[2025-06-05 12:23:12] Iter: 436/20000, R2[135/400], Temp: 0.7443, Energy: -6.275406-0.000916j, Rel_err(%): 1.8855
[2025-06-05 12:23:16] Iter: 437/20000, R2[136/400], Temp: 0.7409, Energy: -6.276378+0.000457j, Rel_err(%): 1.8703
[2025-06-05 12:23:19] Iter: 438/20000, R2[137/400], Temp: 0.7374, Energy: -6.273614-0.001101j, Rel_err(%): 1.9135
[2025-06-05 12:23:22] Iter: 439/20000, R2[138/400], Temp: 0.7340, Energy: -6.274481-0.000444j, Rel_err(%): 1.8999
[2025-06-05 12:23:25] Iter: 440/20000, R2[139/400], Temp: 0.7305, Energy: -6.278153+0.000411j, Rel_err(%): 1.8425
[2025-06-05 12:23:28] Iter: 441/20000, R2[140/400], Temp: 0.7270, Energy: -6.274094+0.000248j, Rel_err(%): 1.9060
[2025-06-05 12:23:31] Iter: 442/20000, R2[141/400], Temp: 0.7235, Energy: -6.274155-0.000120j, Rel_err(%): 1.9050
[2025-06-05 12:23:34] Iter: 443/20000, R2[142/400], Temp: 0.7200, Energy: -6.273763-0.000713j, Rel_err(%): 1.9112
[2025-06-05 12:23:38] Iter: 444/20000, R2[143/400], Temp: 0.7164, Energy: -6.275320-0.001498j, Rel_err(%): 1.8869
[2025-06-05 12:23:41] Iter: 445/20000, R2[144/400], Temp: 0.7129, Energy: -6.272222+0.000068j, Rel_err(%): 1.9352
[2025-06-05 12:23:44] Iter: 446/20000, R2[145/400], Temp: 0.7093, Energy: -6.272492+0.001876j, Rel_err(%): 1.9312
[2025-06-05 12:23:47] Iter: 447/20000, R2[146/400], Temp: 0.7058, Energy: -6.273440-0.001140j, Rel_err(%): 1.9163
[2025-06-05 12:23:50] Iter: 448/20000, R2[147/400], Temp: 0.7022, Energy: -6.274064+0.000668j, Rel_err(%): 1.9065
[2025-06-05 12:23:54] Iter: 449/20000, R2[148/400], Temp: 0.6986, Energy: -6.275391-0.000536j, Rel_err(%): 1.8857
[2025-06-05 12:23:57] Iter: 450/20000, R2[149/400], Temp: 0.6950, Energy: -6.275132+0.001372j, Rel_err(%): 1.8899
[2025-06-05 12:24:00] Iter: 451/20000, R2[150/400], Temp: 0.6913, Energy: -6.274275+0.000426j, Rel_err(%): 1.9031
[2025-06-05 12:24:03] Iter: 452/20000, R2[151/400], Temp: 0.6877, Energy: -6.273171-0.000816j, Rel_err(%): 1.9204
[2025-06-05 12:24:06] Iter: 453/20000, R2[152/400], Temp: 0.6841, Energy: -6.273775+0.001013j, Rel_err(%): 1.9110
[2025-06-05 12:24:09] Iter: 454/20000, R2[153/400], Temp: 0.6804, Energy: -6.271825+0.001683j, Rel_err(%): 1.9416
[2025-06-05 12:24:12] Iter: 455/20000, R2[154/400], Temp: 0.6767, Energy: -6.275926-0.000911j, Rel_err(%): 1.8774
[2025-06-05 12:24:16] Iter: 456/20000, R2[155/400], Temp: 0.6731, Energy: -6.271533-0.000547j, Rel_err(%): 1.9460
[2025-06-05 12:24:19] Iter: 457/20000, R2[156/400], Temp: 0.6694, Energy: -6.273212-0.001602j, Rel_err(%): 1.9199
[2025-06-05 12:24:22] Iter: 458/20000, R2[157/400], Temp: 0.6657, Energy: -6.273195-0.001857j, Rel_err(%): 1.9202
[2025-06-05 12:24:25] Iter: 459/20000, R2[158/400], Temp: 0.6620, Energy: -6.272715+0.001684j, Rel_err(%): 1.9277
[2025-06-05 12:24:28] Iter: 460/20000, R2[159/400], Temp: 0.6582, Energy: -6.272173-0.001408j, Rel_err(%): 1.9361
[2025-06-05 12:24:31] Iter: 461/20000, R2[160/400], Temp: 0.6545, Energy: -6.273472-0.000346j, Rel_err(%): 1.9157
[2025-06-05 12:24:35] Iter: 462/20000, R2[161/400], Temp: 0.6508, Energy: -6.274315+0.000077j, Rel_err(%): 1.9025
[2025-06-05 12:24:38] Iter: 463/20000, R2[162/400], Temp: 0.6470, Energy: -6.276167+0.000582j, Rel_err(%): 1.8736
[2025-06-05 12:24:41] Iter: 464/20000, R2[163/400], Temp: 0.6433, Energy: -6.277560-0.000982j, Rel_err(%): 1.8518
[2025-06-05 12:24:44] Iter: 465/20000, R2[164/400], Temp: 0.6395, Energy: -6.274354+0.001095j, Rel_err(%): 1.9020
[2025-06-05 12:24:47] Iter: 466/20000, R2[165/400], Temp: 0.6357, Energy: -6.275883-0.000377j, Rel_err(%): 1.8780
[2025-06-05 12:24:50] Iter: 467/20000, R2[166/400], Temp: 0.6319, Energy: -6.275330+0.001125j, Rel_err(%): 1.8867
[2025-06-05 12:24:54] Iter: 468/20000, R2[167/400], Temp: 0.6281, Energy: -6.272505+0.000891j, Rel_err(%): 1.9309
[2025-06-05 12:24:57] Iter: 469/20000, R2[168/400], Temp: 0.6243, Energy: -6.274354+0.001300j, Rel_err(%): 1.9020
[2025-06-05 12:25:00] Iter: 470/20000, R2[169/400], Temp: 0.6205, Energy: -6.277263+0.000941j, Rel_err(%): 1.8565
[2025-06-05 12:25:03] Iter: 471/20000, R2[170/400], Temp: 0.6167, Energy: -6.276655-0.000028j, Rel_err(%): 1.8659
[2025-06-05 12:25:06] Iter: 472/20000, R2[171/400], Temp: 0.6129, Energy: -6.276534+0.002391j, Rel_err(%): 1.8682
[2025-06-05 12:25:09] Iter: 473/20000, R2[172/400], Temp: 0.6091, Energy: -6.273578-0.000038j, Rel_err(%): 1.9140
[2025-06-05 12:25:12] Iter: 474/20000, R2[173/400], Temp: 0.6052, Energy: -6.274833-0.002141j, Rel_err(%): 1.8947
[2025-06-05 12:25:16] Iter: 475/20000, R2[174/400], Temp: 0.6014, Energy: -6.274772+0.000593j, Rel_err(%): 1.8954
[2025-06-05 12:25:19] Iter: 476/20000, R2[175/400], Temp: 0.5975, Energy: -6.274432-0.001106j, Rel_err(%): 1.9008
[2025-06-05 12:25:22] Iter: 477/20000, R2[176/400], Temp: 0.5937, Energy: -6.276440-0.001925j, Rel_err(%): 1.8695
[2025-06-05 12:25:25] Iter: 478/20000, R2[177/400], Temp: 0.5898, Energy: -6.276104+0.000610j, Rel_err(%): 1.8746
[2025-06-05 12:25:28] Iter: 479/20000, R2[178/400], Temp: 0.5860, Energy: -6.274837-0.000475j, Rel_err(%): 1.8944
[2025-06-05 12:25:31] Iter: 480/20000, R2[179/400], Temp: 0.5821, Energy: -6.276837-0.000461j, Rel_err(%): 1.8631
[2025-06-05 12:25:35] Iter: 481/20000, R2[180/400], Temp: 0.5782, Energy: -6.276954-0.000684j, Rel_err(%): 1.8613
[2025-06-05 12:25:38] Iter: 482/20000, R2[181/400], Temp: 0.5743, Energy: -6.273065+0.000645j, Rel_err(%): 1.9221
[2025-06-05 12:25:41] Iter: 483/20000, R2[182/400], Temp: 0.5705, Energy: -6.275493-0.001063j, Rel_err(%): 1.8842
[2025-06-05 12:25:44] Iter: 484/20000, R2[183/400], Temp: 0.5666, Energy: -6.275783-0.000908j, Rel_err(%): 1.8796
[2025-06-05 12:25:47] Iter: 485/20000, R2[184/400], Temp: 0.5627, Energy: -6.273103-0.000216j, Rel_err(%): 1.9215
[2025-06-05 12:25:50] Iter: 486/20000, R2[185/400], Temp: 0.5588, Energy: -6.274894-0.002205j, Rel_err(%): 1.8938
[2025-06-05 12:25:54] Iter: 487/20000, R2[186/400], Temp: 0.5549, Energy: -6.276371-0.002048j, Rel_err(%): 1.8706
[2025-06-05 12:25:57] Iter: 488/20000, R2[187/400], Temp: 0.5510, Energy: -6.277600-0.001511j, Rel_err(%): 1.8513
[2025-06-05 12:26:00] Iter: 489/20000, R2[188/400], Temp: 0.5471, Energy: -6.276535+0.000011j, Rel_err(%): 1.8678
[2025-06-05 12:26:03] Iter: 490/20000, R2[189/400], Temp: 0.5431, Energy: -6.276933+0.000997j, Rel_err(%): 1.8617
[2025-06-05 12:26:06] Iter: 491/20000, R2[190/400], Temp: 0.5392, Energy: -6.273672-0.000754j, Rel_err(%): 1.9126
[2025-06-05 12:26:09] Iter: 492/20000, R2[191/400], Temp: 0.5353, Energy: -6.274044-0.001448j, Rel_err(%): 1.9069
[2025-06-05 12:26:12] Iter: 493/20000, R2[192/400], Temp: 0.5314, Energy: -6.274790+0.000359j, Rel_err(%): 1.8951
[2025-06-05 12:26:16] Iter: 494/20000, R2[193/400], Temp: 0.5275, Energy: -6.276583-0.000316j, Rel_err(%): 1.8671
[2025-06-05 12:26:19] Iter: 495/20000, R2[194/400], Temp: 0.5236, Energy: -6.276527-0.001102j, Rel_err(%): 1.8680
[2025-06-05 12:26:22] Iter: 496/20000, R2[195/400], Temp: 0.5196, Energy: -6.274505+0.000146j, Rel_err(%): 1.8996
[2025-06-05 12:26:25] Iter: 497/20000, R2[196/400], Temp: 0.5157, Energy: -6.275557+0.000388j, Rel_err(%): 1.8831
[2025-06-05 12:26:28] Iter: 498/20000, R2[197/400], Temp: 0.5118, Energy: -6.275904+0.000437j, Rel_err(%): 1.8777
[2025-06-05 12:26:31] Iter: 499/20000, R2[198/400], Temp: 0.5079, Energy: -6.275827-0.000403j, Rel_err(%): 1.8789
[2025-06-05 12:26:35] Iter: 500/20000, R2[199/400], Temp: 0.5039, Energy: -6.276371-0.001435j, Rel_err(%): 1.8705
[2025-06-05 12:26:38] Iter: 501/20000, R2[200/400], Temp: 0.5000, Energy: -6.275917+0.000207j, Rel_err(%): 1.8775
[2025-06-05 12:26:41] Iter: 502/20000, R2[201/400], Temp: 0.4961, Energy: -6.275080+0.000315j, Rel_err(%): 1.8906
[2025-06-05 12:26:44] Iter: 503/20000, R2[202/400], Temp: 0.4921, Energy: -6.275544+0.001264j, Rel_err(%): 1.8834
[2025-06-05 12:26:47] Iter: 504/20000, R2[203/400], Temp: 0.4882, Energy: -6.275832-0.000122j, Rel_err(%): 1.8788
[2025-06-05 12:26:50] Iter: 505/20000, R2[204/400], Temp: 0.4843, Energy: -6.276301+0.000122j, Rel_err(%): 1.8715
[2025-06-05 12:26:54] Iter: 506/20000, R2[205/400], Temp: 0.4804, Energy: -6.273241+0.000849j, Rel_err(%): 1.9194
[2025-06-05 12:26:57] Iter: 507/20000, R2[206/400], Temp: 0.4764, Energy: -6.273925-0.001215j, Rel_err(%): 1.9087
[2025-06-05 12:27:00] Iter: 508/20000, R2[207/400], Temp: 0.4725, Energy: -6.274592-0.000762j, Rel_err(%): 1.8982
[2025-06-05 12:27:03] Iter: 509/20000, R2[208/400], Temp: 0.4686, Energy: -6.276461-0.003347j, Rel_err(%): 1.8697
[2025-06-05 12:27:06] Iter: 510/20000, R2[209/400], Temp: 0.4647, Energy: -6.275748-0.000741j, Rel_err(%): 1.8802
[2025-06-05 12:27:09] Iter: 511/20000, R2[210/400], Temp: 0.4608, Energy: -6.274764-0.000735j, Rel_err(%): 1.8955
[2025-06-05 12:27:12] Iter: 512/20000, R2[211/400], Temp: 0.4569, Energy: -6.272807-0.001896j, Rel_err(%): 1.9263
[2025-06-05 12:27:16] Iter: 513/20000, R2[212/400], Temp: 0.4529, Energy: -6.276048-0.001217j, Rel_err(%): 1.8755
[2025-06-05 12:27:19] Iter: 514/20000, R2[213/400], Temp: 0.4490, Energy: -6.274660-0.002089j, Rel_err(%): 1.8974
[2025-06-05 12:27:22] Iter: 515/20000, R2[214/400], Temp: 0.4451, Energy: -6.277974+0.000722j, Rel_err(%): 1.8453
[2025-06-05 12:27:25] Iter: 516/20000, R2[215/400], Temp: 0.4412, Energy: -6.275728-0.000860j, Rel_err(%): 1.8805
[2025-06-05 12:27:28] Iter: 517/20000, R2[216/400], Temp: 0.4373, Energy: -6.277829-0.001188j, Rel_err(%): 1.8477
[2025-06-05 12:27:31] Iter: 518/20000, R2[217/400], Temp: 0.4334, Energy: -6.276716+0.000220j, Rel_err(%): 1.8650
[2025-06-05 12:27:35] Iter: 519/20000, R2[218/400], Temp: 0.4295, Energy: -6.276993+0.000572j, Rel_err(%): 1.8607
[2025-06-05 12:27:38] Iter: 520/20000, R2[219/400], Temp: 0.4257, Energy: -6.277975-0.000287j, Rel_err(%): 1.8453
[2025-06-05 12:27:41] Iter: 521/20000, R2[220/400], Temp: 0.4218, Energy: -6.273317-0.001422j, Rel_err(%): 1.9182
[2025-06-05 12:27:44] Iter: 522/20000, R2[221/400], Temp: 0.4179, Energy: -6.276325+0.000208j, Rel_err(%): 1.8711
[2025-06-05 12:27:47] Iter: 523/20000, R2[222/400], Temp: 0.4140, Energy: -6.274975-0.001478j, Rel_err(%): 1.8923
[2025-06-05 12:27:50] Iter: 524/20000, R2[223/400], Temp: 0.4102, Energy: -6.274914+0.000138j, Rel_err(%): 1.8932
[2025-06-05 12:27:54] Iter: 525/20000, R2[224/400], Temp: 0.4063, Energy: -6.275750-0.003032j, Rel_err(%): 1.8807
[2025-06-05 12:27:57] Iter: 526/20000, R2[225/400], Temp: 0.4025, Energy: -6.274063+0.001280j, Rel_err(%): 1.9066
[2025-06-05 12:28:00] Iter: 527/20000, R2[226/400], Temp: 0.3986, Energy: -6.276017-0.000992j, Rel_err(%): 1.8760
[2025-06-05 12:28:03] Iter: 528/20000, R2[227/400], Temp: 0.3948, Energy: -6.276864+0.000136j, Rel_err(%): 1.8627
[2025-06-05 12:28:06] Iter: 529/20000, R2[228/400], Temp: 0.3909, Energy: -6.274568+0.001754j, Rel_err(%): 1.8988
[2025-06-05 12:28:09] Iter: 530/20000, R2[229/400], Temp: 0.3871, Energy: -6.275444-0.002148j, Rel_err(%): 1.8852
[2025-06-05 12:28:12] Iter: 531/20000, R2[230/400], Temp: 0.3833, Energy: -6.278580+0.000937j, Rel_err(%): 1.8359
[2025-06-05 12:28:16] Iter: 532/20000, R2[231/400], Temp: 0.3795, Energy: -6.278179-0.001783j, Rel_err(%): 1.8423
[2025-06-05 12:28:19] Iter: 533/20000, R2[232/400], Temp: 0.3757, Energy: -6.275270+0.002550j, Rel_err(%): 1.8880
[2025-06-05 12:28:22] Iter: 534/20000, R2[233/400], Temp: 0.3719, Energy: -6.276090-0.003411j, Rel_err(%): 1.8755
[2025-06-05 12:28:25] Iter: 535/20000, R2[234/400], Temp: 0.3681, Energy: -6.273353-0.000019j, Rel_err(%): 1.9176
[2025-06-05 12:28:28] Iter: 536/20000, R2[235/400], Temp: 0.3643, Energy: -6.276326+0.002103j, Rel_err(%): 1.8714
[2025-06-05 12:28:31] Iter: 537/20000, R2[236/400], Temp: 0.3605, Energy: -6.275970+0.000877j, Rel_err(%): 1.8767
[2025-06-05 12:28:35] Iter: 538/20000, R2[237/400], Temp: 0.3567, Energy: -6.275488-0.002049j, Rel_err(%): 1.8844
[2025-06-05 12:28:38] Iter: 539/20000, R2[238/400], Temp: 0.3530, Energy: -6.274747-0.000901j, Rel_err(%): 1.8958
[2025-06-05 12:28:41] Iter: 540/20000, R2[239/400], Temp: 0.3492, Energy: -6.276013+0.002937j, Rel_err(%): 1.8765
[2025-06-05 12:28:44] Iter: 541/20000, R2[240/400], Temp: 0.3455, Energy: -6.273942-0.000654j, Rel_err(%): 1.9084
[2025-06-05 12:28:47] Iter: 542/20000, R2[241/400], Temp: 0.3418, Energy: -6.275637+0.001793j, Rel_err(%): 1.8821
[2025-06-05 12:28:50] Iter: 543/20000, R2[242/400], Temp: 0.3380, Energy: -6.277513+0.002097j, Rel_err(%): 1.8528
[2025-06-05 12:28:54] Iter: 544/20000, R2[243/400], Temp: 0.3343, Energy: -6.277867+0.001915j, Rel_err(%): 1.8472
[2025-06-05 12:28:57] Iter: 545/20000, R2[244/400], Temp: 0.3306, Energy: -6.274897+0.000136j, Rel_err(%): 1.8934
[2025-06-05 12:29:00] Iter: 546/20000, R2[245/400], Temp: 0.3269, Energy: -6.273621-0.000576j, Rel_err(%): 1.9134
[2025-06-05 12:29:03] Iter: 547/20000, R2[246/400], Temp: 0.3233, Energy: -6.276795-0.000544j, Rel_err(%): 1.8638
[2025-06-05 12:29:06] Iter: 548/20000, R2[247/400], Temp: 0.3196, Energy: -6.274952-0.000575j, Rel_err(%): 1.8926
[2025-06-05 12:29:09] Iter: 549/20000, R2[248/400], Temp: 0.3159, Energy: -6.276908-0.001616j, Rel_err(%): 1.8621
[2025-06-05 12:29:12] Iter: 550/20000, R2[249/400], Temp: 0.3123, Energy: -6.277163+0.000553j, Rel_err(%): 1.8580
[2025-06-05 12:29:16] Iter: 551/20000, R2[250/400], Temp: 0.3087, Energy: -6.276485-0.001052j, Rel_err(%): 1.8687
[2025-06-05 12:29:19] Iter: 552/20000, R2[251/400], Temp: 0.3050, Energy: -6.276625+0.000527j, Rel_err(%): 1.8664
[2025-06-05 12:29:22] Iter: 553/20000, R2[252/400], Temp: 0.3014, Energy: -6.277965+0.000194j, Rel_err(%): 1.8455
[2025-06-05 12:29:25] Iter: 554/20000, R2[253/400], Temp: 0.2978, Energy: -6.273509-0.000731j, Rel_err(%): 1.9152
[2025-06-05 12:29:28] Iter: 555/20000, R2[254/400], Temp: 0.2942, Energy: -6.275999+0.000479j, Rel_err(%): 1.8762
[2025-06-05 12:29:31] Iter: 556/20000, R2[255/400], Temp: 0.2907, Energy: -6.277842+0.001070j, Rel_err(%): 1.8475
[2025-06-05 12:29:35] Iter: 557/20000, R2[256/400], Temp: 0.2871, Energy: -6.275720-0.000078j, Rel_err(%): 1.8805
[2025-06-05 12:29:38] Iter: 558/20000, R2[257/400], Temp: 0.2836, Energy: -6.275372+0.000273j, Rel_err(%): 1.8860
[2025-06-05 12:29:41] Iter: 559/20000, R2[258/400], Temp: 0.2800, Energy: -6.278057+0.000709j, Rel_err(%): 1.8440
[2025-06-05 12:29:44] Iter: 560/20000, R2[259/400], Temp: 0.2765, Energy: -6.277231-0.000618j, Rel_err(%): 1.8570
[2025-06-05 12:29:47] Iter: 561/20000, R2[260/400], Temp: 0.2730, Energy: -6.277836-0.000110j, Rel_err(%): 1.8475
[2025-06-05 12:29:50] Iter: 562/20000, R2[261/400], Temp: 0.2695, Energy: -6.276989-0.001363j, Rel_err(%): 1.8608
[2025-06-05 12:29:54] Iter: 563/20000, R2[262/400], Temp: 0.2660, Energy: -6.275395+0.000835j, Rel_err(%): 1.8857
[2025-06-05 12:29:57] Iter: 564/20000, R2[263/400], Temp: 0.2626, Energy: -6.276759-0.001655j, Rel_err(%): 1.8645
[2025-06-05 12:30:00] Iter: 565/20000, R2[264/400], Temp: 0.2591, Energy: -6.276843-0.001618j, Rel_err(%): 1.8632
[2025-06-05 12:30:03] Iter: 566/20000, R2[265/400], Temp: 0.2557, Energy: -6.276916-0.000580j, Rel_err(%): 1.8619
[2025-06-05 12:30:06] Iter: 567/20000, R2[266/400], Temp: 0.2523, Energy: -6.276604+0.000781j, Rel_err(%): 1.8668
[2025-06-05 12:30:09] Iter: 568/20000, R2[267/400], Temp: 0.2489, Energy: -6.277165+0.000383j, Rel_err(%): 1.8580
[2025-06-05 12:30:12] Iter: 569/20000, R2[268/400], Temp: 0.2455, Energy: -6.277208+0.000674j, Rel_err(%): 1.8573
[2025-06-05 12:30:16] Iter: 570/20000, R2[269/400], Temp: 0.2421, Energy: -6.278245-0.001287j, Rel_err(%): 1.8412
[2025-06-05 12:30:19] Iter: 571/20000, R2[270/400], Temp: 0.2388, Energy: -6.277895-0.001023j, Rel_err(%): 1.8466
[2025-06-05 12:30:22] Iter: 572/20000, R2[271/400], Temp: 0.2354, Energy: -6.276808+0.000093j, Rel_err(%): 1.8635
[2025-06-05 12:30:25] Iter: 573/20000, R2[272/400], Temp: 0.2321, Energy: -6.278675-0.001898j, Rel_err(%): 1.8346
[2025-06-05 12:30:28] Iter: 574/20000, R2[273/400], Temp: 0.2288, Energy: -6.275853+0.000608j, Rel_err(%): 1.8785
[2025-06-05 12:30:31] Iter: 575/20000, R2[274/400], Temp: 0.2255, Energy: -6.276162-0.000590j, Rel_err(%): 1.8737
[2025-06-05 12:30:35] Iter: 576/20000, R2[275/400], Temp: 0.2222, Energy: -6.276893+0.000212j, Rel_err(%): 1.8622
[2025-06-05 12:30:38] Iter: 577/20000, R2[276/400], Temp: 0.2190, Energy: -6.275404-0.000862j, Rel_err(%): 1.8855
[2025-06-05 12:30:41] Iter: 578/20000, R2[277/400], Temp: 0.2157, Energy: -6.275398+0.001062j, Rel_err(%): 1.8857
[2025-06-05 12:30:44] Iter: 579/20000, R2[278/400], Temp: 0.2125, Energy: -6.275432+0.001376j, Rel_err(%): 1.8852
[2025-06-05 12:30:47] Iter: 580/20000, R2[279/400], Temp: 0.2093, Energy: -6.274056+0.001287j, Rel_err(%): 1.9067
[2025-06-05 12:30:50] Iter: 581/20000, R2[280/400], Temp: 0.2061, Energy: -6.274541-0.000174j, Rel_err(%): 1.8990
[2025-06-05 12:30:53] Iter: 582/20000, R2[281/400], Temp: 0.2029, Energy: -6.277685-0.001434j, Rel_err(%): 1.8500
[2025-06-05 12:30:57] Iter: 583/20000, R2[282/400], Temp: 0.1998, Energy: -6.275172+0.001376j, Rel_err(%): 1.8892
[2025-06-05 12:31:00] Iter: 584/20000, R2[283/400], Temp: 0.1967, Energy: -6.279110-0.000671j, Rel_err(%): 1.8276
[2025-06-05 12:31:03] Iter: 585/20000, R2[284/400], Temp: 0.1935, Energy: -6.276250-0.001219j, Rel_err(%): 1.8724
[2025-06-05 12:31:06] Iter: 586/20000, R2[285/400], Temp: 0.1905, Energy: -6.275197-0.001694j, Rel_err(%): 1.8889
[2025-06-05 12:31:09] Iter: 587/20000, R2[286/400], Temp: 0.1874, Energy: -6.275270-0.002501j, Rel_err(%): 1.8880
[2025-06-05 12:31:12] Iter: 588/20000, R2[287/400], Temp: 0.1843, Energy: -6.275676-0.001546j, Rel_err(%): 1.8814
[2025-06-05 12:31:16] Iter: 589/20000, R2[288/400], Temp: 0.1813, Energy: -6.278019-0.001301j, Rel_err(%): 1.8447
[2025-06-05 12:31:19] Iter: 590/20000, R2[289/400], Temp: 0.1783, Energy: -6.278771-0.001309j, Rel_err(%): 1.8330
[2025-06-05 12:31:22] Iter: 591/20000, R2[290/400], Temp: 0.1753, Energy: -6.274091-0.001852j, Rel_err(%): 1.9062
[2025-06-05 12:31:25] Iter: 592/20000, R2[291/400], Temp: 0.1723, Energy: -6.275778-0.001075j, Rel_err(%): 1.8797
[2025-06-05 12:31:28] Iter: 593/20000, R2[292/400], Temp: 0.1693, Energy: -6.276250-0.000153j, Rel_err(%): 1.8723
[2025-06-05 12:31:31] Iter: 594/20000, R2[293/400], Temp: 0.1664, Energy: -6.279209-0.000442j, Rel_err(%): 1.8260
[2025-06-05 12:31:35] Iter: 595/20000, R2[294/400], Temp: 0.1635, Energy: -6.277678-0.001161j, Rel_err(%): 1.8500
[2025-06-05 12:31:38] Iter: 596/20000, R2[295/400], Temp: 0.1606, Energy: -6.274574-0.001976j, Rel_err(%): 1.8987
[2025-06-05 12:31:41] Iter: 597/20000, R2[296/400], Temp: 0.1577, Energy: -6.274301-0.001560j, Rel_err(%): 1.9029
[2025-06-05 12:31:44] Iter: 598/20000, R2[297/400], Temp: 0.1549, Energy: -6.275109+0.000172j, Rel_err(%): 1.8901
[2025-06-05 12:31:47] Iter: 599/20000, R2[298/400], Temp: 0.1520, Energy: -6.276600-0.002337j, Rel_err(%): 1.8671
[2025-06-05 12:31:50] Iter: 600/20000, R2[299/400], Temp: 0.1492, Energy: -6.274854+0.001181j, Rel_err(%): 1.8942
[2025-06-05 12:31:53] Iter: 601/20000, R2[300/400], Temp: 0.1464, Energy: -6.276276-0.000886j, Rel_err(%): 1.8719
[2025-06-05 12:31:57] Iter: 602/20000, R2[301/400], Temp: 0.1437, Energy: -6.275541+0.000210j, Rel_err(%): 1.8833
[2025-06-05 12:32:00] Iter: 603/20000, R2[302/400], Temp: 0.1409, Energy: -6.275928-0.000823j, Rel_err(%): 1.8773
[2025-06-05 12:32:03] Iter: 604/20000, R2[303/400], Temp: 0.1382, Energy: -6.275884-0.002056j, Rel_err(%): 1.8783
[2025-06-05 12:32:06] Iter: 605/20000, R2[304/400], Temp: 0.1355, Energy: -6.277819-0.000437j, Rel_err(%): 1.8477
[2025-06-05 12:32:09] Iter: 606/20000, R2[305/400], Temp: 0.1328, Energy: -6.278049+0.001012j, Rel_err(%): 1.8442
[2025-06-05 12:32:12] Iter: 607/20000, R2[306/400], Temp: 0.1302, Energy: -6.277573-0.001703j, Rel_err(%): 1.8518
[2025-06-05 12:32:16] Iter: 608/20000, R2[307/400], Temp: 0.1276, Energy: -6.275949-0.000595j, Rel_err(%): 1.8770
[2025-06-05 12:32:19] Iter: 609/20000, R2[308/400], Temp: 0.1249, Energy: -6.276272-0.000548j, Rel_err(%): 1.8719
[2025-06-05 12:32:22] Iter: 610/20000, R2[309/400], Temp: 0.1224, Energy: -6.276666+0.002119j, Rel_err(%): 1.8661
[2025-06-05 12:32:25] Iter: 611/20000, R2[310/400], Temp: 0.1198, Energy: -6.277927-0.000879j, Rel_err(%): 1.8461
[2025-06-05 12:32:28] Iter: 612/20000, R2[311/400], Temp: 0.1173, Energy: -6.277362+0.000591j, Rel_err(%): 1.8549
[2025-06-05 12:32:31] Iter: 613/20000, R2[312/400], Temp: 0.1147, Energy: -6.275332-0.000923j, Rel_err(%): 1.8867
[2025-06-05 12:32:34] Iter: 614/20000, R2[313/400], Temp: 0.1123, Energy: -6.276807-0.000952j, Rel_err(%): 1.8636
[2025-06-05 12:32:38] Iter: 615/20000, R2[314/400], Temp: 0.1098, Energy: -6.277803-0.001906j, Rel_err(%): 1.8482
[2025-06-05 12:32:41] Iter: 616/20000, R2[315/400], Temp: 0.1073, Energy: -6.278122-0.000034j, Rel_err(%): 1.8430
[2025-06-05 12:32:44] Iter: 617/20000, R2[316/400], Temp: 0.1049, Energy: -6.278479-0.001016j, Rel_err(%): 1.8375
[2025-06-05 12:32:47] Iter: 618/20000, R2[317/400], Temp: 0.1025, Energy: -6.276759-0.000008j, Rel_err(%): 1.8643
[2025-06-05 12:32:50] Iter: 619/20000, R2[318/400], Temp: 0.1002, Energy: -6.277169+0.001557j, Rel_err(%): 1.8581
[2025-06-05 12:32:54] Iter: 620/20000, R2[319/400], Temp: 0.0978, Energy: -6.277437+0.000779j, Rel_err(%): 1.8537
[2025-06-05 12:32:57] Iter: 621/20000, R2[320/400], Temp: 0.0955, Energy: -6.275665+0.000596j, Rel_err(%): 1.8814
[2025-06-05 12:33:00] Iter: 622/20000, R2[321/400], Temp: 0.0932, Energy: -6.277614+0.002024j, Rel_err(%): 1.8512
[2025-06-05 12:33:03] Iter: 623/20000, R2[322/400], Temp: 0.0909, Energy: -6.278302-0.000032j, Rel_err(%): 1.8402
[2025-06-05 12:33:06] Iter: 624/20000, R2[323/400], Temp: 0.0887, Energy: -6.277841-0.002039j, Rel_err(%): 1.8477
[2025-06-05 12:33:09] Iter: 625/20000, R2[324/400], Temp: 0.0865, Energy: -6.275605-0.000085j, Rel_err(%): 1.8824
[2025-06-05 12:33:12] Iter: 626/20000, R2[325/400], Temp: 0.0843, Energy: -6.273621-0.000385j, Rel_err(%): 1.9134
[2025-06-05 12:33:16] Iter: 627/20000, R2[326/400], Temp: 0.0821, Energy: -6.276297+0.001174j, Rel_err(%): 1.8716
[2025-06-05 12:33:19] Iter: 628/20000, R2[327/400], Temp: 0.0800, Energy: -6.276343+0.000116j, Rel_err(%): 1.8708
[2025-06-05 12:33:22] Iter: 629/20000, R2[328/400], Temp: 0.0778, Energy: -6.277993+0.000643j, Rel_err(%): 1.8450
[2025-06-05 12:33:25] Iter: 630/20000, R2[329/400], Temp: 0.0757, Energy: -6.276438+0.001366j, Rel_err(%): 1.8694
[2025-06-05 12:33:28] Iter: 631/20000, R2[330/400], Temp: 0.0737, Energy: -6.276474+0.000050j, Rel_err(%): 1.8688
[2025-06-05 12:33:31] Iter: 632/20000, R2[331/400], Temp: 0.0716, Energy: -6.276095+0.001563j, Rel_err(%): 1.8748
[2025-06-05 12:33:35] Iter: 633/20000, R2[332/400], Temp: 0.0696, Energy: -6.275209-0.000879j, Rel_err(%): 1.8886
[2025-06-05 12:33:38] Iter: 634/20000, R2[333/400], Temp: 0.0676, Energy: -6.276439+0.001078j, Rel_err(%): 1.8694
[2025-06-05 12:33:41] Iter: 635/20000, R2[334/400], Temp: 0.0657, Energy: -6.278309-0.000152j, Rel_err(%): 1.8401
[2025-06-05 12:33:44] Iter: 636/20000, R2[335/400], Temp: 0.0638, Energy: -6.277872-0.001593j, Rel_err(%): 1.8471
[2025-06-05 12:33:47] Iter: 637/20000, R2[336/400], Temp: 0.0618, Energy: -6.273050-0.000249j, Rel_err(%): 1.9223
[2025-06-05 12:33:50] Iter: 638/20000, R2[337/400], Temp: 0.0600, Energy: -6.277217-0.001877j, Rel_err(%): 1.8574
[2025-06-05 12:33:54] Iter: 639/20000, R2[338/400], Temp: 0.0581, Energy: -6.276163+0.001867j, Rel_err(%): 1.8739
[2025-06-05 12:33:57] Iter: 640/20000, R2[339/400], Temp: 0.0563, Energy: -6.278748+0.000862j, Rel_err(%): 1.8333
[2025-06-05 12:34:00] Iter: 641/20000, R2[340/400], Temp: 0.0545, Energy: -6.278988+0.000257j, Rel_err(%): 1.8295
[2025-06-05 12:34:03] Iter: 642/20000, R2[341/400], Temp: 0.0527, Energy: -6.277262+0.002613j, Rel_err(%): 1.8569
[2025-06-05 12:34:06] Iter: 643/20000, R2[342/400], Temp: 0.0510, Energy: -6.277329+0.000022j, Rel_err(%): 1.8554
[2025-06-05 12:34:09] Iter: 644/20000, R2[343/400], Temp: 0.0493, Energy: -6.277755-0.000819j, Rel_err(%): 1.8488
[2025-06-05 12:34:12] Iter: 645/20000, R2[344/400], Temp: 0.0476, Energy: -6.277758+0.001270j, Rel_err(%): 1.8488
[2025-06-05 12:34:16] Iter: 646/20000, R2[345/400], Temp: 0.0459, Energy: -6.276019-0.002760j, Rel_err(%): 1.8764
[2025-06-05 12:34:19] Iter: 647/20000, R2[346/400], Temp: 0.0443, Energy: -6.275898-0.000037j, Rel_err(%): 1.8778
[2025-06-05 12:34:22] Iter: 648/20000, R2[347/400], Temp: 0.0427, Energy: -6.275805-0.001976j, Rel_err(%): 1.8795
[2025-06-05 12:34:25] Iter: 649/20000, R2[348/400], Temp: 0.0411, Energy: -6.277433-0.000503j, Rel_err(%): 1.8538
[2025-06-05 12:34:28] Iter: 650/20000, R2[349/400], Temp: 0.0396, Energy: -6.277935+0.001966j, Rel_err(%): 1.8462
[2025-06-05 12:34:31] Iter: 651/20000, R2[350/400], Temp: 0.0381, Energy: -6.276937+0.000344j, Rel_err(%): 1.8615
[2025-06-05 12:34:35] Iter: 652/20000, R2[351/400], Temp: 0.0366, Energy: -6.277260-0.001575j, Rel_err(%): 1.8566
[2025-06-05 12:34:38] Iter: 653/20000, R2[352/400], Temp: 0.0351, Energy: -6.278166+0.000789j, Rel_err(%): 1.8424
[2025-06-05 12:34:41] Iter: 654/20000, R2[353/400], Temp: 0.0337, Energy: -6.275790+0.001517j, Rel_err(%): 1.8796
[2025-06-05 12:34:44] Iter: 655/20000, R2[354/400], Temp: 0.0323, Energy: -6.274098-0.000854j, Rel_err(%): 1.9060
[2025-06-05 12:34:47] Iter: 656/20000, R2[355/400], Temp: 0.0309, Energy: -6.277539-0.000983j, Rel_err(%): 1.8522
[2025-06-05 12:34:50] Iter: 657/20000, R2[356/400], Temp: 0.0296, Energy: -6.277968+0.000335j, Rel_err(%): 1.8454
[2025-06-05 12:34:54] Iter: 658/20000, R2[357/400], Temp: 0.0282, Energy: -6.275833-0.001322j, Rel_err(%): 1.8789
[2025-06-05 12:34:57] Iter: 659/20000, R2[358/400], Temp: 0.0270, Energy: -6.276376-0.001330j, Rel_err(%): 1.8704
[2025-06-05 12:35:00] Iter: 660/20000, R2[359/400], Temp: 0.0257, Energy: -6.277705-0.000941j, Rel_err(%): 1.8496
[2025-06-05 12:35:03] Iter: 661/20000, R2[360/400], Temp: 0.0245, Energy: -6.276238-0.000374j, Rel_err(%): 1.8725
[2025-06-05 12:35:06] Iter: 662/20000, R2[361/400], Temp: 0.0233, Energy: -6.276904+0.000825j, Rel_err(%): 1.8621
[2025-06-05 12:35:09] Iter: 663/20000, R2[362/400], Temp: 0.0221, Energy: -6.275818+0.000361j, Rel_err(%): 1.8790
[2025-06-05 12:35:12] Iter: 664/20000, R2[363/400], Temp: 0.0210, Energy: -6.279932-0.000309j, Rel_err(%): 1.8147
[2025-06-05 12:35:16] Iter: 665/20000, R2[364/400], Temp: 0.0199, Energy: -6.276757-0.000462j, Rel_err(%): 1.8644
[2025-06-05 12:35:19] Iter: 666/20000, R2[365/400], Temp: 0.0188, Energy: -6.274385-0.000792j, Rel_err(%): 1.9015
[2025-06-05 12:35:22] Iter: 667/20000, R2[366/400], Temp: 0.0177, Energy: -6.277654+0.000587j, Rel_err(%): 1.8503
[2025-06-05 12:35:25] Iter: 668/20000, R2[367/400], Temp: 0.0167, Energy: -6.276351-0.002150j, Rel_err(%): 1.8710
[2025-06-05 12:35:28] Iter: 669/20000, R2[368/400], Temp: 0.0157, Energy: -6.277041-0.000645j, Rel_err(%): 1.8599
[2025-06-05 12:35:31] Iter: 670/20000, R2[369/400], Temp: 0.0147, Energy: -6.279408-0.000294j, Rel_err(%): 1.8229
[2025-06-05 12:35:35] Iter: 671/20000, R2[370/400], Temp: 0.0138, Energy: -6.279113+0.001150j, Rel_err(%): 1.8276
[2025-06-05 12:35:38] Iter: 672/20000, R2[371/400], Temp: 0.0129, Energy: -6.276367+0.000512j, Rel_err(%): 1.8705
[2025-06-05 12:35:41] Iter: 673/20000, R2[372/400], Temp: 0.0120, Energy: -6.278156-0.000773j, Rel_err(%): 1.8425
[2025-06-05 12:35:44] Iter: 674/20000, R2[373/400], Temp: 0.0112, Energy: -6.278126-0.000899j, Rel_err(%): 1.8430
[2025-06-05 12:35:47] Iter: 675/20000, R2[374/400], Temp: 0.0104, Energy: -6.278582+0.000133j, Rel_err(%): 1.8358
[2025-06-05 12:35:50] Iter: 676/20000, R2[375/400], Temp: 0.0096, Energy: -6.278088+0.000509j, Rel_err(%): 1.8435
[2025-06-05 12:35:54] Iter: 677/20000, R2[376/400], Temp: 0.0089, Energy: -6.277964+0.001278j, Rel_err(%): 1.8456
[2025-06-05 12:35:57] Iter: 678/20000, R2[377/400], Temp: 0.0081, Energy: -6.276692+0.001695j, Rel_err(%): 1.8655
[2025-06-05 12:36:00] Iter: 679/20000, R2[378/400], Temp: 0.0074, Energy: -6.280176+0.000884j, Rel_err(%): 1.8109
[2025-06-05 12:36:03] Iter: 680/20000, R2[379/400], Temp: 0.0068, Energy: -6.279105-0.000946j, Rel_err(%): 1.8277
[2025-06-05 12:36:06] Iter: 681/20000, R2[380/400], Temp: 0.0062, Energy: -6.279760-0.000353j, Rel_err(%): 1.8174
[2025-06-05 12:36:09] Iter: 682/20000, R2[381/400], Temp: 0.0056, Energy: -6.277353-0.000678j, Rel_err(%): 1.8550
[2025-06-05 12:36:12] Iter: 683/20000, R2[382/400], Temp: 0.0050, Energy: -6.278238-0.001071j, Rel_err(%): 1.8413
[2025-06-05 12:36:16] Iter: 684/20000, R2[383/400], Temp: 0.0045, Energy: -6.278589-0.001404j, Rel_err(%): 1.8358
[2025-06-05 12:36:19] Iter: 685/20000, R2[384/400], Temp: 0.0039, Energy: -6.277592-0.000295j, Rel_err(%): 1.8513
[2025-06-05 12:36:22] Iter: 686/20000, R2[385/400], Temp: 0.0035, Energy: -6.278412-0.002095j, Rel_err(%): 1.8388
[2025-06-05 12:36:25] Iter: 687/20000, R2[386/400], Temp: 0.0030, Energy: -6.279891-0.000073j, Rel_err(%): 1.8153
[2025-06-05 12:36:28] Iter: 688/20000, R2[387/400], Temp: 0.0026, Energy: -6.279311-0.002194j, Rel_err(%): 1.8247
[2025-06-05 12:36:31] Iter: 689/20000, R2[388/400], Temp: 0.0022, Energy: -6.276523-0.002056j, Rel_err(%): 1.8683
[2025-06-05 12:36:35] Iter: 690/20000, R2[389/400], Temp: 0.0019, Energy: -6.276192-0.000203j, Rel_err(%): 1.8732
[2025-06-05 12:36:38] Iter: 691/20000, R2[390/400], Temp: 0.0015, Energy: -6.279691+0.000355j, Rel_err(%): 1.8185
[2025-06-05 12:36:41] Iter: 692/20000, R2[391/400], Temp: 0.0012, Energy: -6.278266-0.000358j, Rel_err(%): 1.8408
[2025-06-05 12:36:44] Iter: 693/20000, R2[392/400], Temp: 0.0010, Energy: -6.279919-0.000369j, Rel_err(%): 1.8149
[2025-06-05 12:36:47] Iter: 694/20000, R2[393/400], Temp: 0.0008, Energy: -6.277947-0.000056j, Rel_err(%): 1.8457
[2025-06-05 12:36:50] Iter: 695/20000, R2[394/400], Temp: 0.0006, Energy: -6.278401-0.000248j, Rel_err(%): 1.8386
[2025-06-05 12:36:53] Iter: 696/20000, R2[395/400], Temp: 0.0004, Energy: -6.277989+0.002243j, Rel_err(%): 1.8454
[2025-06-05 12:36:57] Iter: 697/20000, R2[396/400], Temp: 0.0002, Energy: -6.277858-0.000280j, Rel_err(%): 1.8471
[2025-06-05 12:37:00] Iter: 698/20000, R2[397/400], Temp: 0.0001, Energy: -6.276155-0.000167j, Rel_err(%): 1.8737
[2025-06-05 12:37:03] Iter: 699/20000, R2[398/400], Temp: 0.0001, Energy: -6.278724+0.000163j, Rel_err(%): 1.8336
[2025-06-05 12:37:06] Iter: 700/20000, R2[399/400], Temp: 0.0000, Energy: -6.278301-0.001696j, Rel_err(%): 1.8404
[2025-06-05 12:37:06] RESTART #3 at iteration 701, Temperature reset from 0.0000 to 1.0000, Next period: 800 iterations
[2025-06-05 12:37:09] Iter: 701/20000, R3[0/800], Temp: 1.0000, Energy: -6.277093-0.001239j, Rel_err(%): 1.8592
[2025-06-05 12:37:12] Iter: 702/20000, R3[1/800], Temp: 1.0000, Energy: -6.277185-0.000196j, Rel_err(%): 1.8576
[2025-06-05 12:37:16] Iter: 703/20000, R3[2/800], Temp: 1.0000, Energy: -6.279564-0.000010j, Rel_err(%): 1.8205
[2025-06-05 12:37:19] Iter: 704/20000, R3[3/800], Temp: 1.0000, Energy: -6.279404-0.001829j, Rel_err(%): 1.8232
[2025-06-05 12:37:22] Iter: 705/20000, R3[4/800], Temp: 0.9999, Energy: -6.279614+0.001123j, Rel_err(%): 1.8198
[2025-06-05 12:37:25] Iter: 706/20000, R3[5/800], Temp: 0.9999, Energy: -6.278789-0.000686j, Rel_err(%): 1.8326
[2025-06-05 12:37:28] Iter: 707/20000, R3[6/800], Temp: 0.9999, Energy: -6.277765-0.001835j, Rel_err(%): 1.8488
[2025-06-05 12:37:31] Iter: 708/20000, R3[7/800], Temp: 0.9998, Energy: -6.278370+0.000253j, Rel_err(%): 1.8391
[2025-06-05 12:37:34] Iter: 709/20000, R3[8/800], Temp: 0.9998, Energy: -6.277349-0.001374j, Rel_err(%): 1.8552
[2025-06-05 12:37:38] Iter: 710/20000, R3[9/800], Temp: 0.9997, Energy: -6.276635+0.000363j, Rel_err(%): 1.8663
[2025-06-05 12:37:41] Iter: 711/20000, R3[10/800], Temp: 0.9996, Energy: -6.278560+0.000315j, Rel_err(%): 1.8362
[2025-06-05 12:37:44] Iter: 712/20000, R3[11/800], Temp: 0.9995, Energy: -6.273712-0.000540j, Rel_err(%): 1.9120
[2025-06-05 12:37:47] Iter: 713/20000, R3[12/800], Temp: 0.9994, Energy: -6.277488+0.001286j, Rel_err(%): 1.8530
[2025-06-05 12:37:50] Iter: 714/20000, R3[13/800], Temp: 0.9993, Energy: -6.276413+0.001566j, Rel_err(%): 1.8699
[2025-06-05 12:37:53] Iter: 715/20000, R3[14/800], Temp: 0.9992, Energy: -6.278440-0.000673j, Rel_err(%): 1.8381
[2025-06-05 12:37:57] Iter: 716/20000, R3[15/800], Temp: 0.9991, Energy: -6.277675-0.002118j, Rel_err(%): 1.8503
[2025-06-05 12:38:00] Iter: 717/20000, R3[16/800], Temp: 0.9990, Energy: -6.278709-0.000111j, Rel_err(%): 1.8338
[2025-06-05 12:38:03] Iter: 718/20000, R3[17/800], Temp: 0.9989, Energy: -6.277560+0.000965j, Rel_err(%): 1.8518
[2025-06-05 12:38:06] Iter: 719/20000, R3[18/800], Temp: 0.9988, Energy: -6.279033+0.000194j, Rel_err(%): 1.8288
[2025-06-05 12:38:09] Iter: 720/20000, R3[19/800], Temp: 0.9986, Energy: -6.276644-0.000272j, Rel_err(%): 1.8661
[2025-06-05 12:38:12] Iter: 721/20000, R3[20/800], Temp: 0.9985, Energy: -6.276643-0.000135j, Rel_err(%): 1.8661
[2025-06-05 12:38:16] Iter: 722/20000, R3[21/800], Temp: 0.9983, Energy: -6.277922-0.000664j, Rel_err(%): 1.8462
[2025-06-05 12:38:19] Iter: 723/20000, R3[22/800], Temp: 0.9981, Energy: -6.279315+0.000158j, Rel_err(%): 1.8243
[2025-06-05 12:38:22] Iter: 724/20000, R3[23/800], Temp: 0.9980, Energy: -6.279604+0.001888j, Rel_err(%): 1.8201
[2025-06-05 12:38:25] Iter: 725/20000, R3[24/800], Temp: 0.9978, Energy: -6.276406-0.000815j, Rel_err(%): 1.8699
[2025-06-05 12:38:28] Iter: 726/20000, R3[25/800], Temp: 0.9976, Energy: -6.278734-0.000844j, Rel_err(%): 1.8335
[2025-06-05 12:38:31] Iter: 727/20000, R3[26/800], Temp: 0.9974, Energy: -6.276252-0.000074j, Rel_err(%): 1.8722
[2025-06-05 12:38:34] Iter: 728/20000, R3[27/800], Temp: 0.9972, Energy: -6.276328-0.000778j, Rel_err(%): 1.8711
[2025-06-05 12:38:38] Iter: 729/20000, R3[28/800], Temp: 0.9970, Energy: -6.278118-0.001614j, Rel_err(%): 1.8432
[2025-06-05 12:38:41] Iter: 730/20000, R3[29/800], Temp: 0.9968, Energy: -6.279420+0.000739j, Rel_err(%): 1.8227
[2025-06-05 12:38:44] Iter: 731/20000, R3[30/800], Temp: 0.9965, Energy: -6.279155+0.000636j, Rel_err(%): 1.8269
[2025-06-05 12:38:47] Iter: 732/20000, R3[31/800], Temp: 0.9963, Energy: -6.278979+0.001141j, Rel_err(%): 1.8297
[2025-06-05 12:38:50] Iter: 733/20000, R3[32/800], Temp: 0.9961, Energy: -6.280160-0.000840j, Rel_err(%): 1.8112
[2025-06-05 12:38:54] Iter: 734/20000, R3[33/800], Temp: 0.9958, Energy: -6.278641-0.000360j, Rel_err(%): 1.8349
[2025-06-05 12:38:57] Iter: 735/20000, R3[34/800], Temp: 0.9955, Energy: -6.280197+0.001655j, Rel_err(%): 1.8107
[2025-06-05 12:39:00] Iter: 736/20000, R3[35/800], Temp: 0.9953, Energy: -6.275413-0.000931j, Rel_err(%): 1.8854
[2025-06-05 12:39:03] Iter: 737/20000, R3[36/800], Temp: 0.9950, Energy: -6.278020-0.000684j, Rel_err(%): 1.8446
[2025-06-05 12:39:06] Iter: 738/20000, R3[37/800], Temp: 0.9947, Energy: -6.280377+0.001197j, Rel_err(%): 1.8078
[2025-06-05 12:39:09] Iter: 739/20000, R3[38/800], Temp: 0.9944, Energy: -6.278301+0.000262j, Rel_err(%): 1.8402
[2025-06-05 12:39:12] Iter: 740/20000, R3[39/800], Temp: 0.9941, Energy: -6.278766-0.000965j, Rel_err(%): 1.8330
[2025-06-05 12:39:16] Iter: 741/20000, R3[40/800], Temp: 0.9938, Energy: -6.276369-0.000143j, Rel_err(%): 1.8704
[2025-06-05 12:39:19] Iter: 742/20000, R3[41/800], Temp: 0.9935, Energy: -6.277429+0.000790j, Rel_err(%): 1.8539
[2025-06-05 12:39:22] Iter: 743/20000, R3[42/800], Temp: 0.9932, Energy: -6.275458-0.000599j, Rel_err(%): 1.8847
[2025-06-05 12:39:25] Iter: 744/20000, R3[43/800], Temp: 0.9929, Energy: -6.276919-0.000022j, Rel_err(%): 1.8618
[2025-06-05 12:39:28] Iter: 745/20000, R3[44/800], Temp: 0.9926, Energy: -6.275754+0.000827j, Rel_err(%): 1.8801
[2025-06-05 12:39:31] Iter: 746/20000, R3[45/800], Temp: 0.9922, Energy: -6.279815+0.000140j, Rel_err(%): 1.8165
[2025-06-05 12:39:35] Iter: 747/20000, R3[46/800], Temp: 0.9919, Energy: -6.278652-0.000394j, Rel_err(%): 1.8347
[2025-06-05 12:39:38] Iter: 748/20000, R3[47/800], Temp: 0.9915, Energy: -6.277958+0.001107j, Rel_err(%): 1.8456
[2025-06-05 12:39:41] Iter: 749/20000, R3[48/800], Temp: 0.9911, Energy: -6.277038-0.000278j, Rel_err(%): 1.8600
[2025-06-05 12:39:44] Iter: 750/20000, R3[49/800], Temp: 0.9908, Energy: -6.279067+0.000109j, Rel_err(%): 1.8282
[2025-06-05 12:39:47] Iter: 751/20000, R3[50/800], Temp: 0.9904, Energy: -6.276458+0.000026j, Rel_err(%): 1.8690
[2025-06-05 12:39:50] Iter: 752/20000, R3[51/800], Temp: 0.9900, Energy: -6.276687-0.001362j, Rel_err(%): 1.8656
[2025-06-05 12:39:53] Iter: 753/20000, R3[52/800], Temp: 0.9896, Energy: -6.277922-0.000835j, Rel_err(%): 1.8462
[2025-06-05 12:39:57] Iter: 754/20000, R3[53/800], Temp: 0.9892, Energy: -6.277437-0.000205j, Rel_err(%): 1.8537
[2025-06-05 12:40:00] Iter: 755/20000, R3[54/800], Temp: 0.9888, Energy: -6.279738+0.000869j, Rel_err(%): 1.8178
[2025-06-05 12:40:03] Iter: 756/20000, R3[55/800], Temp: 0.9884, Energy: -6.279175-0.001238j, Rel_err(%): 1.8266
[2025-06-05 12:40:06] Iter: 757/20000, R3[56/800], Temp: 0.9880, Energy: -6.280217-0.000995j, Rel_err(%): 1.8103
[2025-06-05 12:40:09] Iter: 758/20000, R3[57/800], Temp: 0.9875, Energy: -6.278889-0.000065j, Rel_err(%): 1.8310
[2025-06-05 12:40:12] Iter: 759/20000, R3[58/800], Temp: 0.9871, Energy: -6.277964-0.000476j, Rel_err(%): 1.8455
[2025-06-05 12:40:16] Iter: 760/20000, R3[59/800], Temp: 0.9866, Energy: -6.279848+0.000537j, Rel_err(%): 1.8160
[2025-06-05 12:40:19] Iter: 761/20000, R3[60/800], Temp: 0.9862, Energy: -6.279486-0.000549j, Rel_err(%): 1.8217
[2025-06-05 12:40:22] Iter: 762/20000, R3[61/800], Temp: 0.9857, Energy: -6.280007-0.000388j, Rel_err(%): 1.8135
[2025-06-05 12:40:25] Iter: 763/20000, R3[62/800], Temp: 0.9853, Energy: -6.278465-0.001278j, Rel_err(%): 1.8377
[2025-06-05 12:40:28] Iter: 764/20000, R3[63/800], Temp: 0.9848, Energy: -6.280150-0.000061j, Rel_err(%): 1.8113
[2025-06-05 12:40:31] Iter: 765/20000, R3[64/800], Temp: 0.9843, Energy: -6.279601-0.000038j, Rel_err(%): 1.8199
[2025-06-05 12:40:35] Iter: 766/20000, R3[65/800], Temp: 0.9838, Energy: -6.279293+0.001336j, Rel_err(%): 1.8248
[2025-06-05 12:40:38] Iter: 767/20000, R3[66/800], Temp: 0.9833, Energy: -6.279095-0.000517j, Rel_err(%): 1.8278
[2025-06-05 12:40:41] Iter: 768/20000, R3[67/800], Temp: 0.9828, Energy: -6.278184+0.001663j, Rel_err(%): 1.8422
[2025-06-05 12:40:44] Iter: 769/20000, R3[68/800], Temp: 0.9823, Energy: -6.280440-0.000220j, Rel_err(%): 1.8068
[2025-06-05 12:40:47] Iter: 770/20000, R3[69/800], Temp: 0.9818, Energy: -6.279032+0.000920j, Rel_err(%): 1.8288
[2025-06-05 12:40:50] Iter: 771/20000, R3[70/800], Temp: 0.9812, Energy: -6.276777+0.000266j, Rel_err(%): 1.8640
[2025-06-05 12:40:54] Iter: 772/20000, R3[71/800], Temp: 0.9807, Energy: -6.277949+0.000586j, Rel_err(%): 1.8457
[2025-06-05 12:40:57] Iter: 773/20000, R3[72/800], Temp: 0.9801, Energy: -6.281296+0.000800j, Rel_err(%): 1.7934
[2025-06-05 12:41:00] Iter: 774/20000, R3[73/800], Temp: 0.9796, Energy: -6.278760-0.001017j, Rel_err(%): 1.8331
[2025-06-05 12:41:03] Iter: 775/20000, R3[74/800], Temp: 0.9790, Energy: -6.278571-0.001698j, Rel_err(%): 1.8362
[2025-06-05 12:41:06] Iter: 776/20000, R3[75/800], Temp: 0.9785, Energy: -6.279031+0.001520j, Rel_err(%): 1.8289
[2025-06-05 12:41:09] Iter: 777/20000, R3[76/800], Temp: 0.9779, Energy: -6.278733-0.001707j, Rel_err(%): 1.8336
[2025-06-05 12:41:12] Iter: 778/20000, R3[77/800], Temp: 0.9773, Energy: -6.279164+0.000833j, Rel_err(%): 1.8267
[2025-06-05 12:41:16] Iter: 779/20000, R3[78/800], Temp: 0.9767, Energy: -6.279487-0.000331j, Rel_err(%): 1.8217
[2025-06-05 12:41:19] Iter: 780/20000, R3[79/800], Temp: 0.9761, Energy: -6.279119-0.000273j, Rel_err(%): 1.8274
[2025-06-05 12:41:22] Iter: 781/20000, R3[80/800], Temp: 0.9755, Energy: -6.278924-0.003264j, Rel_err(%): 1.8312
[2025-06-05 12:41:25] Iter: 782/20000, R3[81/800], Temp: 0.9749, Energy: -6.279892+0.001195j, Rel_err(%): 1.8154
[2025-06-05 12:41:28] Iter: 783/20000, R3[82/800], Temp: 0.9743, Energy: -6.280320+0.001484j, Rel_err(%): 1.8088
[2025-06-05 12:41:31] Iter: 784/20000, R3[83/800], Temp: 0.9737, Energy: -6.279313+0.000131j, Rel_err(%): 1.8244
[2025-06-05 12:41:35] Iter: 785/20000, R3[84/800], Temp: 0.9730, Energy: -6.279255+0.000069j, Rel_err(%): 1.8253
[2025-06-05 12:41:38] Iter: 786/20000, R3[85/800], Temp: 0.9724, Energy: -6.279734+0.000584j, Rel_err(%): 1.8178
[2025-06-05 12:41:41] Iter: 787/20000, R3[86/800], Temp: 0.9718, Energy: -6.280240+0.000782j, Rel_err(%): 1.8099
[2025-06-05 12:41:44] Iter: 788/20000, R3[87/800], Temp: 0.9711, Energy: -6.278280+0.001800j, Rel_err(%): 1.8407
[2025-06-05 12:41:47] Iter: 789/20000, R3[88/800], Temp: 0.9704, Energy: -6.278027+0.000172j, Rel_err(%): 1.8445
[2025-06-05 12:41:50] Iter: 790/20000, R3[89/800], Temp: 0.9698, Energy: -6.277686-0.000947j, Rel_err(%): 1.8499
[2025-06-05 12:41:53] Iter: 791/20000, R3[90/800], Temp: 0.9691, Energy: -6.276238+0.001034j, Rel_err(%): 1.8725
[2025-06-05 12:41:57] Iter: 792/20000, R3[91/800], Temp: 0.9684, Energy: -6.277322-0.000474j, Rel_err(%): 1.8555
[2025-06-05 12:42:00] Iter: 793/20000, R3[92/800], Temp: 0.9677, Energy: -6.279460+0.000164j, Rel_err(%): 1.8221
[2025-06-05 12:42:03] Iter: 794/20000, R3[93/800], Temp: 0.9670, Energy: -6.279423+0.000940j, Rel_err(%): 1.8227
[2025-06-05 12:42:06] Iter: 795/20000, R3[94/800], Temp: 0.9663, Energy: -6.277246-0.000260j, Rel_err(%): 1.8567
[2025-06-05 12:42:09] Iter: 796/20000, R3[95/800], Temp: 0.9656, Energy: -6.276128+0.000026j, Rel_err(%): 1.8742
[2025-06-05 12:42:12] Iter: 797/20000, R3[96/800], Temp: 0.9649, Energy: -6.277072-0.002265j, Rel_err(%): 1.8597
[2025-06-05 12:42:16] Iter: 798/20000, R3[97/800], Temp: 0.9642, Energy: -6.275432-0.000700j, Rel_err(%): 1.8851
[2025-06-05 12:42:19] Iter: 799/20000, R3[98/800], Temp: 0.9634, Energy: -6.275877+0.000313j, Rel_err(%): 1.8781
[2025-06-05 12:42:22] Iter: 800/20000, R3[99/800], Temp: 0.9627, Energy: -6.276021+0.001581j, Rel_err(%): 1.8760
[2025-06-05 12:42:25] Iter: 801/20000, R3[100/800], Temp: 0.9619, Energy: -6.275986+0.000701j, Rel_err(%): 1.8764
[2025-06-05 12:42:28] Iter: 802/20000, R3[101/800], Temp: 0.9612, Energy: -6.276409-0.000065j, Rel_err(%): 1.8698
[2025-06-05 12:42:31] Iter: 803/20000, R3[102/800], Temp: 0.9604, Energy: -6.276347-0.001175j, Rel_err(%): 1.8708
[2025-06-05 12:42:35] Iter: 804/20000, R3[103/800], Temp: 0.9597, Energy: -6.276661+0.002028j, Rel_err(%): 1.8661
[2025-06-05 12:42:38] Iter: 805/20000, R3[104/800], Temp: 0.9589, Energy: -6.275437-0.000272j, Rel_err(%): 1.8850
[2025-06-05 12:42:41] Iter: 806/20000, R3[105/800], Temp: 0.9581, Energy: -6.278877+0.001362j, Rel_err(%): 1.8313
[2025-06-05 12:42:44] Iter: 807/20000, R3[106/800], Temp: 0.9573, Energy: -6.277035-0.000900j, Rel_err(%): 1.8600
[2025-06-05 12:42:47] Iter: 808/20000, R3[107/800], Temp: 0.9565, Energy: -6.276741+0.000699j, Rel_err(%): 1.8646
[2025-06-05 12:42:50] Iter: 809/20000, R3[108/800], Temp: 0.9557, Energy: -6.278792-0.000031j, Rel_err(%): 1.8325
[2025-06-05 12:42:54] Iter: 810/20000, R3[109/800], Temp: 0.9549, Energy: -6.280283-0.000503j, Rel_err(%): 1.8092
[2025-06-05 12:42:57] Iter: 811/20000, R3[110/800], Temp: 0.9541, Energy: -6.278258+0.000954j, Rel_err(%): 1.8409
[2025-06-05 12:43:00] Iter: 812/20000, R3[111/800], Temp: 0.9532, Energy: -6.279280-0.000907j, Rel_err(%): 1.8250
[2025-06-05 12:43:03] Iter: 813/20000, R3[112/800], Temp: 0.9524, Energy: -6.276567+0.000037j, Rel_err(%): 1.8673
[2025-06-05 12:43:06] Iter: 814/20000, R3[113/800], Temp: 0.9516, Energy: -6.280567-0.000928j, Rel_err(%): 1.8048
[2025-06-05 12:43:09] Iter: 815/20000, R3[114/800], Temp: 0.9507, Energy: -6.279865+0.001146j, Rel_err(%): 1.8158
[2025-06-05 12:43:12] Iter: 816/20000, R3[115/800], Temp: 0.9499, Energy: -6.278603-0.000317j, Rel_err(%): 1.8355
[2025-06-05 12:43:16] Iter: 817/20000, R3[116/800], Temp: 0.9490, Energy: -6.279627+0.002863j, Rel_err(%): 1.8200
[2025-06-05 12:43:19] Iter: 818/20000, R3[117/800], Temp: 0.9481, Energy: -6.277991+0.000014j, Rel_err(%): 1.8450
[2025-06-05 12:43:22] Iter: 819/20000, R3[118/800], Temp: 0.9473, Energy: -6.279200+0.000712j, Rel_err(%): 1.8262
[2025-06-05 12:43:25] Iter: 820/20000, R3[119/800], Temp: 0.9464, Energy: -6.277668-0.001010j, Rel_err(%): 1.8502
[2025-06-05 12:43:28] Iter: 821/20000, R3[120/800], Temp: 0.9455, Energy: -6.278500-0.000803j, Rel_err(%): 1.8371
[2025-06-05 12:43:31] Iter: 822/20000, R3[121/800], Temp: 0.9446, Energy: -6.279013+0.000982j, Rel_err(%): 1.8291
[2025-06-05 12:43:35] Iter: 823/20000, R3[122/800], Temp: 0.9437, Energy: -6.279381+0.000541j, Rel_err(%): 1.8233
[2025-06-05 12:43:38] Iter: 824/20000, R3[123/800], Temp: 0.9428, Energy: -6.278373-0.001578j, Rel_err(%): 1.8392
[2025-06-05 12:43:41] Iter: 825/20000, R3[124/800], Temp: 0.9419, Energy: -6.279020-0.000320j, Rel_err(%): 1.8290
[2025-06-05 12:43:44] Iter: 826/20000, R3[125/800], Temp: 0.9410, Energy: -6.278516-0.001449j, Rel_err(%): 1.8370
[2025-06-05 12:43:47] Iter: 827/20000, R3[126/800], Temp: 0.9400, Energy: -6.279621-0.000471j, Rel_err(%): 1.8196
[2025-06-05 12:43:50] Iter: 828/20000, R3[127/800], Temp: 0.9391, Energy: -6.278719-0.001381j, Rel_err(%): 1.8338
[2025-06-05 12:43:54] Iter: 829/20000, R3[128/800], Temp: 0.9382, Energy: -6.279154-0.000879j, Rel_err(%): 1.8269
[2025-06-05 12:43:57] Iter: 830/20000, R3[129/800], Temp: 0.9372, Energy: -6.279639-0.000771j, Rel_err(%): 1.8193
[2025-06-05 12:44:00] Iter: 831/20000, R3[130/800], Temp: 0.9362, Energy: -6.279970+0.001346j, Rel_err(%): 1.8142
[2025-06-05 12:44:03] Iter: 832/20000, R3[131/800], Temp: 0.9353, Energy: -6.278852+0.001249j, Rel_err(%): 1.8317
[2025-06-05 12:44:06] Iter: 833/20000, R3[132/800], Temp: 0.9343, Energy: -6.280098-0.000637j, Rel_err(%): 1.8121
[2025-06-05 12:44:09] Iter: 834/20000, R3[133/800], Temp: 0.9333, Energy: -6.279467-0.000874j, Rel_err(%): 1.8220
[2025-06-05 12:44:13] Iter: 835/20000, R3[134/800], Temp: 0.9324, Energy: -6.278184+0.000438j, Rel_err(%): 1.8420
[2025-06-05 12:44:16] Iter: 836/20000, R3[135/800], Temp: 0.9314, Energy: -6.276852-0.000561j, Rel_err(%): 1.8629
[2025-06-05 12:44:19] Iter: 837/20000, R3[136/800], Temp: 0.9304, Energy: -6.279804+0.000208j, Rel_err(%): 1.8167
[2025-06-05 12:44:22] Iter: 838/20000, R3[137/800], Temp: 0.9294, Energy: -6.279546+0.000009j, Rel_err(%): 1.8207
[2025-06-05 12:44:25] Iter: 839/20000, R3[138/800], Temp: 0.9284, Energy: -6.279137-0.000199j, Rel_err(%): 1.8271
[2025-06-05 12:44:28] Iter: 840/20000, R3[139/800], Temp: 0.9273, Energy: -6.279706+0.000015j, Rel_err(%): 1.8182
[2025-06-05 12:44:31] Iter: 841/20000, R3[140/800], Temp: 0.9263, Energy: -6.278054-0.001146j, Rel_err(%): 1.8441
[2025-06-05 12:44:35] Iter: 842/20000, R3[141/800], Temp: 0.9253, Energy: -6.277292-0.000697j, Rel_err(%): 1.8560
[2025-06-05 12:44:38] Iter: 843/20000, R3[142/800], Temp: 0.9243, Energy: -6.277565-0.001019j, Rel_err(%): 1.8518
[2025-06-05 12:44:41] Iter: 844/20000, R3[143/800], Temp: 0.9232, Energy: -6.278101-0.000386j, Rel_err(%): 1.8433
[2025-06-05 12:44:44] Iter: 845/20000, R3[144/800], Temp: 0.9222, Energy: -6.277698+0.000334j, Rel_err(%): 1.8496
[2025-06-05 12:44:47] Iter: 846/20000, R3[145/800], Temp: 0.9211, Energy: -6.278245-0.000331j, Rel_err(%): 1.8411
[2025-06-05 12:44:50] Iter: 847/20000, R3[146/800], Temp: 0.9200, Energy: -6.280280+0.000825j, Rel_err(%): 1.8093
[2025-06-05 12:44:54] Iter: 848/20000, R3[147/800], Temp: 0.9190, Energy: -6.278186+0.000683j, Rel_err(%): 1.8420
[2025-06-05 12:44:57] Iter: 849/20000, R3[148/800], Temp: 0.9179, Energy: -6.277981+0.001078j, Rel_err(%): 1.8453
[2025-06-05 12:45:00] Iter: 850/20000, R3[149/800], Temp: 0.9168, Energy: -6.278206-0.000334j, Rel_err(%): 1.8417
[2025-06-05 12:45:03] Iter: 851/20000, R3[150/800], Temp: 0.9157, Energy: -6.279508+0.000792j, Rel_err(%): 1.8214
[2025-06-05 12:45:06] Iter: 852/20000, R3[151/800], Temp: 0.9146, Energy: -6.278743+0.000885j, Rel_err(%): 1.8333
[2025-06-05 12:45:09] Iter: 853/20000, R3[152/800], Temp: 0.9135, Energy: -6.278729-0.000994j, Rel_err(%): 1.8336
[2025-06-05 12:45:13] Iter: 854/20000, R3[153/800], Temp: 0.9124, Energy: -6.277400-0.000517j, Rel_err(%): 1.8543
[2025-06-05 12:45:16] Iter: 855/20000, R3[154/800], Temp: 0.9113, Energy: -6.279722+0.000161j, Rel_err(%): 1.8180
[2025-06-05 12:45:19] Iter: 856/20000, R3[155/800], Temp: 0.9102, Energy: -6.279973+0.001050j, Rel_err(%): 1.8141
[2025-06-05 12:45:22] Iter: 857/20000, R3[156/800], Temp: 0.9091, Energy: -6.278865-0.001350j, Rel_err(%): 1.8315
[2025-06-05 12:45:25] Iter: 858/20000, R3[157/800], Temp: 0.9079, Energy: -6.279688+0.000750j, Rel_err(%): 1.8185
[2025-06-05 12:45:28] Iter: 859/20000, R3[158/800], Temp: 0.9068, Energy: -6.279680-0.000485j, Rel_err(%): 1.8187
[2025-06-05 12:45:31] Iter: 860/20000, R3[159/800], Temp: 0.9057, Energy: -6.279818+0.001186j, Rel_err(%): 1.8166
[2025-06-05 12:45:35] Iter: 861/20000, R3[160/800], Temp: 0.9045, Energy: -6.279249-0.000903j, Rel_err(%): 1.8254
[2025-06-05 12:45:38] Iter: 862/20000, R3[161/800], Temp: 0.9034, Energy: -6.278608+0.000632j, Rel_err(%): 1.8354
[2025-06-05 12:45:41] Iter: 863/20000, R3[162/800], Temp: 0.9022, Energy: -6.278005+0.002939j, Rel_err(%): 1.8454
[2025-06-05 12:45:44] Iter: 864/20000, R3[163/800], Temp: 0.9010, Energy: -6.277487-0.000714j, Rel_err(%): 1.8530
[2025-06-05 12:45:47] Iter: 865/20000, R3[164/800], Temp: 0.8998, Energy: -6.280921-0.000039j, Rel_err(%): 1.7992
[2025-06-05 12:45:50] Iter: 866/20000, R3[165/800], Temp: 0.8987, Energy: -6.277702+0.000591j, Rel_err(%): 1.8496
[2025-06-05 12:45:54] Iter: 867/20000, R3[166/800], Temp: 0.8975, Energy: -6.278883+0.001992j, Rel_err(%): 1.8314
[2025-06-05 12:45:57] Iter: 868/20000, R3[167/800], Temp: 0.8963, Energy: -6.278446-0.000078j, Rel_err(%): 1.8379
[2025-06-05 12:46:00] Iter: 869/20000, R3[168/800], Temp: 0.8951, Energy: -6.278115+0.000879j, Rel_err(%): 1.8432
[2025-06-05 12:46:03] Iter: 870/20000, R3[169/800], Temp: 0.8939, Energy: -6.279166+0.000988j, Rel_err(%): 1.8267
[2025-06-05 12:46:06] Iter: 871/20000, R3[170/800], Temp: 0.8927, Energy: -6.279334-0.000177j, Rel_err(%): 1.8240
[2025-06-05 12:46:09] Iter: 872/20000, R3[171/800], Temp: 0.8914, Energy: -6.279400-0.002308j, Rel_err(%): 1.8234
[2025-06-05 12:46:12] Iter: 873/20000, R3[172/800], Temp: 0.8902, Energy: -6.279805-0.003179j, Rel_err(%): 1.8174
[2025-06-05 12:46:16] Iter: 874/20000, R3[173/800], Temp: 0.8890, Energy: -6.280598+0.000894j, Rel_err(%): 1.8043
[2025-06-05 12:46:19] Iter: 875/20000, R3[174/800], Temp: 0.8877, Energy: -6.278973-0.002144j, Rel_err(%): 1.8300
[2025-06-05 12:46:22] Iter: 876/20000, R3[175/800], Temp: 0.8865, Energy: -6.279075+0.000221j, Rel_err(%): 1.8281
[2025-06-05 12:46:25] Iter: 877/20000, R3[176/800], Temp: 0.8853, Energy: -6.278542+0.000544j, Rel_err(%): 1.8364
[2025-06-05 12:46:28] Iter: 878/20000, R3[177/800], Temp: 0.8840, Energy: -6.277721-0.000109j, Rel_err(%): 1.8493
[2025-06-05 12:46:31] Iter: 879/20000, R3[178/800], Temp: 0.8827, Energy: -6.278200-0.000414j, Rel_err(%): 1.8418
[2025-06-05 12:46:35] Iter: 880/20000, R3[179/800], Temp: 0.8815, Energy: -6.280553-0.000416j, Rel_err(%): 1.8050
[2025-06-05 12:46:38] Iter: 881/20000, R3[180/800], Temp: 0.8802, Energy: -6.279328+0.000425j, Rel_err(%): 1.8241
[2025-06-05 12:46:41] Iter: 882/20000, R3[181/800], Temp: 0.8789, Energy: -6.277791+0.000237j, Rel_err(%): 1.8482
[2025-06-05 12:46:44] Iter: 883/20000, R3[182/800], Temp: 0.8776, Energy: -6.277860-0.000386j, Rel_err(%): 1.8471
[2025-06-05 12:46:47] Iter: 884/20000, R3[183/800], Temp: 0.8764, Energy: -6.279798+0.000168j, Rel_err(%): 1.8168
[2025-06-05 12:46:50] Iter: 885/20000, R3[184/800], Temp: 0.8751, Energy: -6.278699+0.000311j, Rel_err(%): 1.8340
[2025-06-05 12:46:54] Iter: 886/20000, R3[185/800], Temp: 0.8738, Energy: -6.277675-0.000108j, Rel_err(%): 1.8500
[2025-06-05 12:46:57] Iter: 887/20000, R3[186/800], Temp: 0.8724, Energy: -6.280286+0.000177j, Rel_err(%): 1.8092
[2025-06-05 12:47:00] Iter: 888/20000, R3[187/800], Temp: 0.8711, Energy: -6.279354-0.000645j, Rel_err(%): 1.8238
[2025-06-05 12:47:03] Iter: 889/20000, R3[188/800], Temp: 0.8698, Energy: -6.278074+0.001466j, Rel_err(%): 1.8439
[2025-06-05 12:47:06] Iter: 890/20000, R3[189/800], Temp: 0.8685, Energy: -6.279376+0.000197j, Rel_err(%): 1.8234
[2025-06-05 12:47:09] Iter: 891/20000, R3[190/800], Temp: 0.8672, Energy: -6.279374-0.000473j, Rel_err(%): 1.8234
[2025-06-05 12:47:12] Iter: 892/20000, R3[191/800], Temp: 0.8658, Energy: -6.279949+0.000570j, Rel_err(%): 1.8145
[2025-06-05 12:47:16] Iter: 893/20000, R3[192/800], Temp: 0.8645, Energy: -6.279999-0.001725j, Rel_err(%): 1.8139
[2025-06-05 12:47:19] Iter: 894/20000, R3[193/800], Temp: 0.8631, Energy: -6.278195-0.001877j, Rel_err(%): 1.8421
[2025-06-05 12:47:22] Iter: 895/20000, R3[194/800], Temp: 0.8618, Energy: -6.279677+0.001032j, Rel_err(%): 1.8188
[2025-06-05 12:47:25] Iter: 896/20000, R3[195/800], Temp: 0.8604, Energy: -6.277778+0.001725j, Rel_err(%): 1.8486
[2025-06-05 12:47:28] Iter: 897/20000, R3[196/800], Temp: 0.8591, Energy: -6.278873+0.000358j, Rel_err(%): 1.8313
[2025-06-05 12:47:31] Iter: 898/20000, R3[197/800], Temp: 0.8577, Energy: -6.279010-0.000055j, Rel_err(%): 1.8291
[2025-06-05 12:47:35] Iter: 899/20000, R3[198/800], Temp: 0.8563, Energy: -6.280210+0.000114j, Rel_err(%): 1.8103
[2025-06-05 12:47:38] Iter: 900/20000, R3[199/800], Temp: 0.8549, Energy: -6.277152+0.000036j, Rel_err(%): 1.8582
[2025-06-05 12:47:41] Iter: 901/20000, R3[200/800], Temp: 0.8536, Energy: -6.278167+0.000454j, Rel_err(%): 1.8423
[2025-06-05 12:47:44] Iter: 902/20000, R3[201/800], Temp: 0.8522, Energy: -6.278648+0.000442j, Rel_err(%): 1.8348
[2025-06-05 12:47:47] Iter: 903/20000, R3[202/800], Temp: 0.8508, Energy: -6.278810+0.001983j, Rel_err(%): 1.8325
[2025-06-05 12:47:50] Iter: 904/20000, R3[203/800], Temp: 0.8494, Energy: -6.279505-0.000700j, Rel_err(%): 1.8214
[2025-06-05 12:47:54] Iter: 905/20000, R3[204/800], Temp: 0.8480, Energy: -6.278903-0.001094j, Rel_err(%): 1.8309
[2025-06-05 12:47:57] Iter: 906/20000, R3[205/800], Temp: 0.8465, Energy: -6.279346+0.000035j, Rel_err(%): 1.8239
[2025-06-05 12:48:00] Iter: 907/20000, R3[206/800], Temp: 0.8451, Energy: -6.278194-0.000466j, Rel_err(%): 1.8419
[2025-06-05 12:48:03] Iter: 908/20000, R3[207/800], Temp: 0.8437, Energy: -6.278588+0.001502j, Rel_err(%): 1.8359
[2025-06-05 12:48:06] Iter: 909/20000, R3[208/800], Temp: 0.8423, Energy: -6.278461+0.000733j, Rel_err(%): 1.8377
[2025-06-05 12:48:09] Iter: 910/20000, R3[209/800], Temp: 0.8408, Energy: -6.280027+0.000685j, Rel_err(%): 1.8132
[2025-06-05 12:48:12] Iter: 911/20000, R3[210/800], Temp: 0.8394, Energy: -6.279560-0.000846j, Rel_err(%): 1.8206
[2025-06-05 12:48:16] Iter: 912/20000, R3[211/800], Temp: 0.8380, Energy: -6.278920-0.000248j, Rel_err(%): 1.8305
[2025-06-05 12:48:19] Iter: 913/20000, R3[212/800], Temp: 0.8365, Energy: -6.278932-0.000179j, Rel_err(%): 1.8303
[2025-06-05 12:48:22] Iter: 914/20000, R3[213/800], Temp: 0.8351, Energy: -6.283563+0.000546j, Rel_err(%): 1.7580
[2025-06-05 12:48:25] Iter: 915/20000, R3[214/800], Temp: 0.8336, Energy: -6.281687+0.002239j, Rel_err(%): 1.7876
[2025-06-05 12:48:28] Iter: 916/20000, R3[215/800], Temp: 0.8321, Energy: -6.279116+0.001506j, Rel_err(%): 1.8276
[2025-06-05 12:48:31] Iter: 917/20000, R3[216/800], Temp: 0.8307, Energy: -6.279982+0.000352j, Rel_err(%): 1.8139
[2025-06-05 12:48:35] Iter: 918/20000, R3[217/800], Temp: 0.8292, Energy: -6.276250-0.000016j, Rel_err(%): 1.8723
[2025-06-05 12:48:38] Iter: 919/20000, R3[218/800], Temp: 0.8277, Energy: -6.279910-0.000118j, Rel_err(%): 1.8150
[2025-06-05 12:48:41] Iter: 920/20000, R3[219/800], Temp: 0.8262, Energy: -6.280319+0.000171j, Rel_err(%): 1.8086
[2025-06-05 12:48:44] Iter: 921/20000, R3[220/800], Temp: 0.8247, Energy: -6.280342+0.001745j, Rel_err(%): 1.8085
[2025-06-05 12:48:47] Iter: 922/20000, R3[221/800], Temp: 0.8232, Energy: -6.278784+0.000965j, Rel_err(%): 1.8327
[2025-06-05 12:48:50] Iter: 923/20000, R3[222/800], Temp: 0.8217, Energy: -6.279211+0.002696j, Rel_err(%): 1.8265
[2025-06-05 12:48:54] Iter: 924/20000, R3[223/800], Temp: 0.8202, Energy: -6.281615-0.001580j, Rel_err(%): 1.7886
[2025-06-05 12:48:57] Iter: 925/20000, R3[224/800], Temp: 0.8187, Energy: -6.279044-0.001549j, Rel_err(%): 1.8287
[2025-06-05 12:49:00] Iter: 926/20000, R3[225/800], Temp: 0.8172, Energy: -6.279472+0.000882j, Rel_err(%): 1.8219
[2025-06-05 12:49:03] Iter: 927/20000, R3[226/800], Temp: 0.8157, Energy: -6.278929-0.001743j, Rel_err(%): 1.8306
[2025-06-05 12:49:06] Iter: 928/20000, R3[227/800], Temp: 0.8142, Energy: -6.277944+0.000159j, Rel_err(%): 1.8458
[2025-06-05 12:49:09] Iter: 929/20000, R3[228/800], Temp: 0.8126, Energy: -6.281103+0.000587j, Rel_err(%): 1.7964
[2025-06-05 12:49:12] Iter: 930/20000, R3[229/800], Temp: 0.8111, Energy: -6.280085-0.000927j, Rel_err(%): 1.8124
[2025-06-05 12:49:16] Iter: 931/20000, R3[230/800], Temp: 0.8095, Energy: -6.282139-0.000600j, Rel_err(%): 1.7802
[2025-06-05 12:49:19] Iter: 932/20000, R3[231/800], Temp: 0.8080, Energy: -6.277988+0.000185j, Rel_err(%): 1.8451
[2025-06-05 12:49:22] Iter: 933/20000, R3[232/800], Temp: 0.8065, Energy: -6.277673-0.000387j, Rel_err(%): 1.8500
[2025-06-05 12:49:25] Iter: 934/20000, R3[233/800], Temp: 0.8049, Energy: -6.280611-0.000276j, Rel_err(%): 1.8041
[2025-06-05 12:49:28] Iter: 935/20000, R3[234/800], Temp: 0.8033, Energy: -6.278876-0.000354j, Rel_err(%): 1.8312
[2025-06-05 12:49:31] Iter: 936/20000, R3[235/800], Temp: 0.8018, Energy: -6.280734-0.000690j, Rel_err(%): 1.8022
[2025-06-05 12:49:35] Iter: 937/20000, R3[236/800], Temp: 0.8002, Energy: -6.278850-0.001532j, Rel_err(%): 1.8318
[2025-06-05 12:49:38] Iter: 938/20000, R3[237/800], Temp: 0.7986, Energy: -6.281433+0.000503j, Rel_err(%): 1.7912
[2025-06-05 12:49:41] Iter: 939/20000, R3[238/800], Temp: 0.7971, Energy: -6.281880-0.001312j, Rel_err(%): 1.7844
[2025-06-05 12:49:44] Iter: 940/20000, R3[239/800], Temp: 0.7955, Energy: -6.279685-0.000212j, Rel_err(%): 1.8186
[2025-06-05 12:49:47] Iter: 941/20000, R3[240/800], Temp: 0.7939, Energy: -6.281480+0.000833j, Rel_err(%): 1.7905
[2025-06-05 12:49:50] Iter: 942/20000, R3[241/800], Temp: 0.7923, Energy: -6.280020-0.000694j, Rel_err(%): 1.8134
[2025-06-05 12:49:54] Iter: 943/20000, R3[242/800], Temp: 0.7907, Energy: -6.281468-0.000199j, Rel_err(%): 1.7907
[2025-06-05 12:49:57] Iter: 944/20000, R3[243/800], Temp: 0.7891, Energy: -6.279557+0.000392j, Rel_err(%): 1.8206
[2025-06-05 12:50:00] Iter: 945/20000, R3[244/800], Temp: 0.7875, Energy: -6.280019-0.000516j, Rel_err(%): 1.8134
[2025-06-05 12:50:03] Iter: 946/20000, R3[245/800], Temp: 0.7859, Energy: -6.279915+0.000674j, Rel_err(%): 1.8150
[2025-06-05 12:50:06] Iter: 947/20000, R3[246/800], Temp: 0.7843, Energy: -6.278443-0.000157j, Rel_err(%): 1.8380
[2025-06-05 12:50:09] Iter: 948/20000, R3[247/800], Temp: 0.7827, Energy: -6.279117-0.000386j, Rel_err(%): 1.8274
[2025-06-05 12:50:12] Iter: 949/20000, R3[248/800], Temp: 0.7810, Energy: -6.279131+0.000206j, Rel_err(%): 1.8272
[2025-06-05 12:50:16] Iter: 950/20000, R3[249/800], Temp: 0.7794, Energy: -6.277758+0.000738j, Rel_err(%): 1.8487
[2025-06-05 12:50:19] Iter: 951/20000, R3[250/800], Temp: 0.7778, Energy: -6.279406-0.000030j, Rel_err(%): 1.8229
[2025-06-05 12:50:22] Iter: 952/20000, R3[251/800], Temp: 0.7762, Energy: -6.279832+0.001425j, Rel_err(%): 1.8164
[2025-06-05 12:50:25] Iter: 953/20000, R3[252/800], Temp: 0.7745, Energy: -6.279100+0.001394j, Rel_err(%): 1.8278
[2025-06-05 12:50:28] Iter: 954/20000, R3[253/800], Temp: 0.7729, Energy: -6.280838-0.000916j, Rel_err(%): 1.8006
[2025-06-05 12:50:31] Iter: 955/20000, R3[254/800], Temp: 0.7712, Energy: -6.279933-0.001271j, Rel_err(%): 1.8148
[2025-06-05 12:50:35] Iter: 956/20000, R3[255/800], Temp: 0.7696, Energy: -6.278706+0.000954j, Rel_err(%): 1.8339
[2025-06-05 12:50:38] Iter: 957/20000, R3[256/800], Temp: 0.7679, Energy: -6.279699-0.000986j, Rel_err(%): 1.8184
[2025-06-05 12:50:41] Iter: 958/20000, R3[257/800], Temp: 0.7663, Energy: -6.278975-0.000726j, Rel_err(%): 1.8297
[2025-06-05 12:50:44] Iter: 959/20000, R3[258/800], Temp: 0.7646, Energy: -6.280499-0.001437j, Rel_err(%): 1.8060
[2025-06-05 12:50:47] Iter: 960/20000, R3[259/800], Temp: 0.7629, Energy: -6.279919-0.001332j, Rel_err(%): 1.8150
[2025-06-05 12:50:50] Iter: 961/20000, R3[260/800], Temp: 0.7612, Energy: -6.277505-0.001376j, Rel_err(%): 1.8528
[2025-06-05 12:50:54] Iter: 962/20000, R3[261/800], Temp: 0.7596, Energy: -6.278703-0.000645j, Rel_err(%): 1.8339
[2025-06-05 12:50:57] Iter: 963/20000, R3[262/800], Temp: 0.7579, Energy: -6.280812+0.000420j, Rel_err(%): 1.8009
[2025-06-05 12:51:00] Iter: 964/20000, R3[263/800], Temp: 0.7562, Energy: -6.280248+0.001151j, Rel_err(%): 1.8098
[2025-06-05 12:51:03] Iter: 965/20000, R3[264/800], Temp: 0.7545, Energy: -6.279932-0.000444j, Rel_err(%): 1.8147
[2025-06-05 12:51:06] Iter: 966/20000, R3[265/800], Temp: 0.7528, Energy: -6.279875+0.000547j, Rel_err(%): 1.8156
[2025-06-05 12:51:09] Iter: 967/20000, R3[266/800], Temp: 0.7511, Energy: -6.277802-0.000361j, Rel_err(%): 1.8480
[2025-06-05 12:51:13] Iter: 968/20000, R3[267/800], Temp: 0.7494, Energy: -6.277091-0.001097j, Rel_err(%): 1.8592
[2025-06-05 12:51:16] Iter: 969/20000, R3[268/800], Temp: 0.7477, Energy: -6.280087-0.003105j, Rel_err(%): 1.8129
[2025-06-05 12:51:19] Iter: 970/20000, R3[269/800], Temp: 0.7460, Energy: -6.277830-0.001247j, Rel_err(%): 1.8477
[2025-06-05 12:51:22] Iter: 971/20000, R3[270/800], Temp: 0.7443, Energy: -6.280440-0.001023j, Rel_err(%): 1.8068
[2025-06-05 12:51:25] Iter: 972/20000, R3[271/800], Temp: 0.7426, Energy: -6.278895-0.001124j, Rel_err(%): 1.8310
[2025-06-05 12:51:28] Iter: 973/20000, R3[272/800], Temp: 0.7409, Energy: -6.280860-0.001351j, Rel_err(%): 1.8003
[2025-06-05 12:51:31] Iter: 974/20000, R3[273/800], Temp: 0.7392, Energy: -6.280332-0.001783j, Rel_err(%): 1.8087
[2025-06-05 12:51:35] Iter: 975/20000, R3[274/800], Temp: 0.7374, Energy: -6.279372-0.001357j, Rel_err(%): 1.8236
[2025-06-05 12:51:38] Iter: 976/20000, R3[275/800], Temp: 0.7357, Energy: -6.280037-0.000612j, Rel_err(%): 1.8131
[2025-06-05 12:51:41] Iter: 977/20000, R3[276/800], Temp: 0.7340, Energy: -6.277293+0.000575j, Rel_err(%): 1.8560
[2025-06-05 12:51:44] Iter: 978/20000, R3[277/800], Temp: 0.7322, Energy: -6.279609+0.000932j, Rel_err(%): 1.8198
[2025-06-05 12:51:47] Iter: 979/20000, R3[278/800], Temp: 0.7305, Energy: -6.277533+0.000332j, Rel_err(%): 1.8522
[2025-06-05 12:51:50] Iter: 980/20000, R3[279/800], Temp: 0.7287, Energy: -6.280211-0.002071j, Rel_err(%): 1.8106
[2025-06-05 12:51:54] Iter: 981/20000, R3[280/800], Temp: 0.7270, Energy: -6.280676+0.000116j, Rel_err(%): 1.8031
[2025-06-05 12:51:57] Iter: 982/20000, R3[281/800], Temp: 0.7252, Energy: -6.280734+0.001689j, Rel_err(%): 1.8024
[2025-06-05 12:52:00] Iter: 983/20000, R3[282/800], Temp: 0.7235, Energy: -6.279125-0.000179j, Rel_err(%): 1.8273
[2025-06-05 12:52:03] Iter: 984/20000, R3[283/800], Temp: 0.7217, Energy: -6.280614-0.000535j, Rel_err(%): 1.8041
[2025-06-05 12:52:06] Iter: 985/20000, R3[284/800], Temp: 0.7200, Energy: -6.279088-0.000071j, Rel_err(%): 1.8279
[2025-06-05 12:52:09] Iter: 986/20000, R3[285/800], Temp: 0.7182, Energy: -6.280488-0.002040j, Rel_err(%): 1.8063
[2025-06-05 12:52:12] Iter: 987/20000, R3[286/800], Temp: 0.7164, Energy: -6.279198-0.002482j, Rel_err(%): 1.8266
[2025-06-05 12:52:16] Iter: 988/20000, R3[287/800], Temp: 0.7147, Energy: -6.281491-0.000306j, Rel_err(%): 1.7903
[2025-06-05 12:52:19] Iter: 989/20000, R3[288/800], Temp: 0.7129, Energy: -6.277079-0.000698j, Rel_err(%): 1.8593
[2025-06-05 12:52:22] Iter: 990/20000, R3[289/800], Temp: 0.7111, Energy: -6.280363-0.000865j, Rel_err(%): 1.8080
[2025-06-05 12:52:25] Iter: 991/20000, R3[290/800], Temp: 0.7093, Energy: -6.279319-0.001092j, Rel_err(%): 1.8244
[2025-06-05 12:52:28] Iter: 992/20000, R3[291/800], Temp: 0.7075, Energy: -6.277809+0.000911j, Rel_err(%): 1.8479
[2025-06-05 12:52:31] Iter: 993/20000, R3[292/800], Temp: 0.7058, Energy: -6.280162-0.000238j, Rel_err(%): 1.8111
[2025-06-05 12:52:35] Iter: 994/20000, R3[293/800], Temp: 0.7040, Energy: -6.279778-0.000314j, Rel_err(%): 1.8171
[2025-06-05 12:52:38] Iter: 995/20000, R3[294/800], Temp: 0.7022, Energy: -6.279970-0.002147j, Rel_err(%): 1.8144
[2025-06-05 12:52:41] Iter: 996/20000, R3[295/800], Temp: 0.7004, Energy: -6.282416+0.002224j, Rel_err(%): 1.7762
[2025-06-05 12:52:44] Iter: 997/20000, R3[296/800], Temp: 0.6986, Energy: -6.279590+0.000406j, Rel_err(%): 1.8201
[2025-06-05 12:52:47] Iter: 998/20000, R3[297/800], Temp: 0.6968, Energy: -6.280637-0.000140j, Rel_err(%): 1.8037
[2025-06-05 12:52:50] Iter: 999/20000, R3[298/800], Temp: 0.6950, Energy: -6.282362-0.001267j, Rel_err(%): 1.7768
[2025-06-05 12:52:54] Iter: 1000/20000, R3[299/800], Temp: 0.6932, Energy: -6.279596-0.000642j, Rel_err(%): 1.8200
[2025-06-05 12:52:57] Iter: 1001/20000, R3[300/800], Temp: 0.6913, Energy: -6.278067+0.000442j, Rel_err(%): 1.8439
[2025-06-05 12:53:00] Iter: 1002/20000, R3[301/800], Temp: 0.6895, Energy: -6.279898-0.000896j, Rel_err(%): 1.8153
[2025-06-05 12:53:03] Iter: 1003/20000, R3[302/800], Temp: 0.6877, Energy: -6.279508+0.000206j, Rel_err(%): 1.8213
[2025-06-05 12:53:06] Iter: 1004/20000, R3[303/800], Temp: 0.6859, Energy: -6.280452+0.000035j, Rel_err(%): 1.8066
[2025-06-05 12:53:09] Iter: 1005/20000, R3[304/800], Temp: 0.6841, Energy: -6.279267+0.000533j, Rel_err(%): 1.8251
[2025-06-05 12:53:12] Iter: 1006/20000, R3[305/800], Temp: 0.6822, Energy: -6.279245-0.000455j, Rel_err(%): 1.8254
[2025-06-05 12:53:16] Iter: 1007/20000, R3[306/800], Temp: 0.6804, Energy: -6.281289-0.000722j, Rel_err(%): 1.7935
[2025-06-05 12:53:19] Iter: 1008/20000, R3[307/800], Temp: 0.6786, Energy: -6.280914+0.001029j, Rel_err(%): 1.7994
[2025-06-05 12:53:22] Iter: 1009/20000, R3[308/800], Temp: 0.6767, Energy: -6.279356+0.000290j, Rel_err(%): 1.8237
[2025-06-05 12:53:25] Iter: 1010/20000, R3[309/800], Temp: 0.6749, Energy: -6.281047-0.001350j, Rel_err(%): 1.7974
[2025-06-05 12:53:28] Iter: 1011/20000, R3[310/800], Temp: 0.6731, Energy: -6.279511-0.000859j, Rel_err(%): 1.8213
[2025-06-05 12:53:31] Iter: 1012/20000, R3[311/800], Temp: 0.6712, Energy: -6.280009-0.001010j, Rel_err(%): 1.8136
[2025-06-05 12:53:35] Iter: 1013/20000, R3[312/800], Temp: 0.6694, Energy: -6.278128+0.000786j, Rel_err(%): 1.8429
[2025-06-05 12:53:38] Iter: 1014/20000, R3[313/800], Temp: 0.6675, Energy: -6.279267-0.001199j, Rel_err(%): 1.8252
[2025-06-05 12:53:41] Iter: 1015/20000, R3[314/800], Temp: 0.6657, Energy: -6.281816-0.000055j, Rel_err(%): 1.7852
[2025-06-05 12:53:44] Iter: 1016/20000, R3[315/800], Temp: 0.6638, Energy: -6.279368+0.001130j, Rel_err(%): 1.8236
[2025-06-05 12:53:47] Iter: 1017/20000, R3[316/800], Temp: 0.6620, Energy: -6.279986-0.000282j, Rel_err(%): 1.8139
[2025-06-05 12:53:50] Iter: 1018/20000, R3[317/800], Temp: 0.6601, Energy: -6.279784+0.001050j, Rel_err(%): 1.8171
[2025-06-05 12:53:54] Iter: 1019/20000, R3[318/800], Temp: 0.6582, Energy: -6.281055+0.001413j, Rel_err(%): 1.7973
[2025-06-05 12:53:57] Iter: 1020/20000, R3[319/800], Temp: 0.6564, Energy: -6.280883+0.000590j, Rel_err(%): 1.7999
[2025-06-05 12:54:00] Iter: 1021/20000, R3[320/800], Temp: 0.6545, Energy: -6.278437+0.000022j, Rel_err(%): 1.8381
[2025-06-05 12:54:03] Iter: 1022/20000, R3[321/800], Temp: 0.6526, Energy: -6.280482+0.001974j, Rel_err(%): 1.8064
[2025-06-05 12:54:06] Iter: 1023/20000, R3[322/800], Temp: 0.6508, Energy: -6.282399-0.000861j, Rel_err(%): 1.7762
[2025-06-05 12:54:09] Iter: 1024/20000, R3[323/800], Temp: 0.6489, Energy: -6.280076-0.000173j, Rel_err(%): 1.8124
[2025-06-05 12:54:12] Iter: 1025/20000, R3[324/800], Temp: 0.6470, Energy: -6.279519+0.000124j, Rel_err(%): 1.8212
[2025-06-05 12:54:16] Iter: 1026/20000, R3[325/800], Temp: 0.6451, Energy: -6.280213+0.000073j, Rel_err(%): 1.8103
[2025-06-05 12:54:19] Iter: 1027/20000, R3[326/800], Temp: 0.6433, Energy: -6.280256-0.001057j, Rel_err(%): 1.8097
[2025-06-05 12:54:22] Iter: 1028/20000, R3[327/800], Temp: 0.6414, Energy: -6.278579+0.000373j, Rel_err(%): 1.8359
[2025-06-05 12:54:25] Iter: 1029/20000, R3[328/800], Temp: 0.6395, Energy: -6.277578+0.000371j, Rel_err(%): 1.8515
[2025-06-05 12:54:28] Iter: 1030/20000, R3[329/800], Temp: 0.6376, Energy: -6.279089+0.001335j, Rel_err(%): 1.8280
[2025-06-05 12:54:31] Iter: 1031/20000, R3[330/800], Temp: 0.6357, Energy: -6.280211+0.001168j, Rel_err(%): 1.8104
[2025-06-05 12:54:35] Iter: 1032/20000, R3[331/800], Temp: 0.6338, Energy: -6.277376+0.001188j, Rel_err(%): 1.8547
[2025-06-05 12:54:38] Iter: 1033/20000, R3[332/800], Temp: 0.6319, Energy: -6.279037+0.000456j, Rel_err(%): 1.8287
[2025-06-05 12:54:41] Iter: 1034/20000, R3[333/800], Temp: 0.6300, Energy: -6.279479+0.001400j, Rel_err(%): 1.8219
[2025-06-05 12:54:44] Iter: 1035/20000, R3[334/800], Temp: 0.6281, Energy: -6.278714+0.000017j, Rel_err(%): 1.8337
[2025-06-05 12:54:47] Iter: 1036/20000, R3[335/800], Temp: 0.6262, Energy: -6.281332-0.000860j, Rel_err(%): 1.7929
[2025-06-05 12:54:50] Iter: 1037/20000, R3[336/800], Temp: 0.6243, Energy: -6.282393-0.001146j, Rel_err(%): 1.7763
[2025-06-05 12:54:54] Iter: 1038/20000, R3[337/800], Temp: 0.6224, Energy: -6.279777+0.001188j, Rel_err(%): 1.8172
[2025-06-05 12:54:57] Iter: 1039/20000, R3[338/800], Temp: 0.6205, Energy: -6.279018+0.000220j, Rel_err(%): 1.8290
[2025-06-05 12:55:00] Iter: 1040/20000, R3[339/800], Temp: 0.6186, Energy: -6.279394-0.000247j, Rel_err(%): 1.8231
[2025-06-05 12:55:03] Iter: 1041/20000, R3[340/800], Temp: 0.6167, Energy: -6.281595-0.001229j, Rel_err(%): 1.7888
[2025-06-05 12:55:06] Iter: 1042/20000, R3[341/800], Temp: 0.6148, Energy: -6.278747+0.000256j, Rel_err(%): 1.8332
[2025-06-05 12:55:09] Iter: 1043/20000, R3[342/800], Temp: 0.6129, Energy: -6.280753+0.000741j, Rel_err(%): 1.8019
[2025-06-05 12:55:13] Iter: 1044/20000, R3[343/800], Temp: 0.6110, Energy: -6.281305-0.000704j, Rel_err(%): 1.7933
[2025-06-05 12:55:16] Iter: 1045/20000, R3[344/800], Temp: 0.6091, Energy: -6.280831+0.001230j, Rel_err(%): 1.8007
[2025-06-05 12:55:19] Iter: 1046/20000, R3[345/800], Temp: 0.6072, Energy: -6.279542+0.000768j, Rel_err(%): 1.8208
[2025-06-05 12:55:22] Iter: 1047/20000, R3[346/800], Temp: 0.6052, Energy: -6.281451+0.000099j, Rel_err(%): 1.7910
[2025-06-05 12:55:25] Iter: 1048/20000, R3[347/800], Temp: 0.6033, Energy: -6.278780+0.000521j, Rel_err(%): 1.8327
[2025-06-05 12:55:28] Iter: 1049/20000, R3[348/800], Temp: 0.6014, Energy: -6.277846-0.000388j, Rel_err(%): 1.8473
[2025-06-05 12:55:31] Iter: 1050/20000, R3[349/800], Temp: 0.5995, Energy: -6.278690+0.000378j, Rel_err(%): 1.8341
[2025-06-05 12:55:35] Iter: 1051/20000, R3[350/800], Temp: 0.5975, Energy: -6.281527-0.000562j, Rel_err(%): 1.7898
[2025-06-05 12:55:38] Iter: 1052/20000, R3[351/800], Temp: 0.5956, Energy: -6.279501-0.000448j, Rel_err(%): 1.8215
[2025-06-05 12:55:41] Iter: 1053/20000, R3[352/800], Temp: 0.5937, Energy: -6.279116-0.000696j, Rel_err(%): 1.8275
[2025-06-05 12:55:44] Iter: 1054/20000, R3[353/800], Temp: 0.5918, Energy: -6.278517+0.000290j, Rel_err(%): 1.8368
[2025-06-05 12:55:47] Iter: 1055/20000, R3[354/800], Temp: 0.5898, Energy: -6.281379-0.001609j, Rel_err(%): 1.7923
[2025-06-05 12:55:50] Iter: 1056/20000, R3[355/800], Temp: 0.5879, Energy: -6.281882-0.000901j, Rel_err(%): 1.7843
[2025-06-05 12:55:54] Iter: 1057/20000, R3[356/800], Temp: 0.5860, Energy: -6.279589+0.000696j, Rel_err(%): 1.8201
[2025-06-05 12:55:57] Iter: 1058/20000, R3[357/800], Temp: 0.5840, Energy: -6.281928-0.001907j, Rel_err(%): 1.7837
[2025-06-05 12:56:00] Iter: 1059/20000, R3[358/800], Temp: 0.5821, Energy: -6.282091-0.000382j, Rel_err(%): 1.7809
[2025-06-05 12:56:03] Iter: 1060/20000, R3[359/800], Temp: 0.5802, Energy: -6.281639+0.000071j, Rel_err(%): 1.7880
[2025-06-05 12:56:06] Iter: 1061/20000, R3[360/800], Temp: 0.5782, Energy: -6.280805+0.000140j, Rel_err(%): 1.8010
[2025-06-05 12:56:09] Iter: 1062/20000, R3[361/800], Temp: 0.5763, Energy: -6.281702-0.002164j, Rel_err(%): 1.7873
[2025-06-05 12:56:12] Iter: 1063/20000, R3[362/800], Temp: 0.5743, Energy: -6.280314-0.000158j, Rel_err(%): 1.8087
[2025-06-05 12:56:16] Iter: 1064/20000, R3[363/800], Temp: 0.5724, Energy: -6.279949+0.000395j, Rel_err(%): 1.8144
[2025-06-05 12:56:19] Iter: 1065/20000, R3[364/800], Temp: 0.5705, Energy: -6.277003-0.000700j, Rel_err(%): 1.8605
[2025-06-05 12:56:22] Iter: 1066/20000, R3[365/800], Temp: 0.5685, Energy: -6.280343+0.000634j, Rel_err(%): 1.8083
[2025-06-05 12:56:25] Iter: 1067/20000, R3[366/800], Temp: 0.5666, Energy: -6.280510-0.001047j, Rel_err(%): 1.8057
[2025-06-05 12:56:28] Iter: 1068/20000, R3[367/800], Temp: 0.5646, Energy: -6.278788+0.001522j, Rel_err(%): 1.8327
[2025-06-05 12:56:31] Iter: 1069/20000, R3[368/800], Temp: 0.5627, Energy: -6.280300+0.001722j, Rel_err(%): 1.8091
[2025-06-05 12:56:35] Iter: 1070/20000, R3[369/800], Temp: 0.5607, Energy: -6.280526-0.002606j, Rel_err(%): 1.8059
[2025-06-05 12:56:38] Iter: 1071/20000, R3[370/800], Temp: 0.5588, Energy: -6.279825-0.000279j, Rel_err(%): 1.8164
[2025-06-05 12:56:41] Iter: 1072/20000, R3[371/800], Temp: 0.5568, Energy: -6.279627-0.000042j, Rel_err(%): 1.8195
[2025-06-05 12:56:44] Iter: 1073/20000, R3[372/800], Temp: 0.5549, Energy: -6.278552-0.000051j, Rel_err(%): 1.8363
[2025-06-05 12:56:47] Iter: 1074/20000, R3[373/800], Temp: 0.5529, Energy: -6.281642-0.001521j, Rel_err(%): 1.7881
[2025-06-05 12:56:50] Iter: 1075/20000, R3[374/800], Temp: 0.5510, Energy: -6.279720-0.000171j, Rel_err(%): 1.8180
[2025-06-05 12:56:54] Iter: 1076/20000, R3[375/800], Temp: 0.5490, Energy: -6.279900-0.000049j, Rel_err(%): 1.8152
[2025-06-05 12:56:57] Iter: 1077/20000, R3[376/800], Temp: 0.5471, Energy: -6.281120-0.000707j, Rel_err(%): 1.7962
[2025-06-05 12:57:00] Iter: 1078/20000, R3[377/800], Temp: 0.5451, Energy: -6.280862-0.000367j, Rel_err(%): 1.8002
[2025-06-05 12:57:03] Iter: 1079/20000, R3[378/800], Temp: 0.5431, Energy: -6.280751-0.000696j, Rel_err(%): 1.8019
[2025-06-05 12:57:06] Iter: 1080/20000, R3[379/800], Temp: 0.5412, Energy: -6.279342-0.000555j, Rel_err(%): 1.8239
[2025-06-05 12:57:09] Iter: 1081/20000, R3[380/800], Temp: 0.5392, Energy: -6.281574-0.000624j, Rel_err(%): 1.7890
[2025-06-05 12:57:13] Iter: 1082/20000, R3[381/800], Temp: 0.5373, Energy: -6.279525-0.000929j, Rel_err(%): 1.8211
[2025-06-05 12:57:16] Iter: 1083/20000, R3[382/800], Temp: 0.5353, Energy: -6.279157+0.000924j, Rel_err(%): 1.8269
[2025-06-05 12:57:19] Iter: 1084/20000, R3[383/800], Temp: 0.5334, Energy: -6.280192+0.000458j, Rel_err(%): 1.8107
[2025-06-05 12:57:22] Iter: 1085/20000, R3[384/800], Temp: 0.5314, Energy: -6.278916+0.000152j, Rel_err(%): 1.8306
[2025-06-05 12:57:25] Iter: 1086/20000, R3[385/800], Temp: 0.5294, Energy: -6.281176-0.000849j, Rel_err(%): 1.7953
[2025-06-05 12:57:28] Iter: 1087/20000, R3[386/800], Temp: 0.5275, Energy: -6.280905+0.001005j, Rel_err(%): 1.7996
[2025-06-05 12:57:32] Iter: 1088/20000, R3[387/800], Temp: 0.5255, Energy: -6.280588-0.001220j, Rel_err(%): 1.8045
[2025-06-05 12:57:35] Iter: 1089/20000, R3[388/800], Temp: 0.5236, Energy: -6.280516+0.000668j, Rel_err(%): 1.8056
[2025-06-05 12:57:38] Iter: 1090/20000, R3[389/800], Temp: 0.5216, Energy: -6.280413+0.001040j, Rel_err(%): 1.8072
[2025-06-05 12:57:41] Iter: 1091/20000, R3[390/800], Temp: 0.5196, Energy: -6.279688-0.000697j, Rel_err(%): 1.8185
[2025-06-05 12:57:44] Iter: 1092/20000, R3[391/800], Temp: 0.5177, Energy: -6.279961-0.001005j, Rel_err(%): 1.8143
[2025-06-05 12:57:47] Iter: 1093/20000, R3[392/800], Temp: 0.5157, Energy: -6.280494-0.001434j, Rel_err(%): 1.8061
[2025-06-05 12:57:50] Iter: 1094/20000, R3[393/800], Temp: 0.5137, Energy: -6.278628-0.000974j, Rel_err(%): 1.8352
[2025-06-05 12:57:54] Iter: 1095/20000, R3[394/800], Temp: 0.5118, Energy: -6.279059+0.000463j, Rel_err(%): 1.8284
[2025-06-05 12:57:57] Iter: 1096/20000, R3[395/800], Temp: 0.5098, Energy: -6.281900-0.000087j, Rel_err(%): 1.7839
[2025-06-05 12:58:00] Iter: 1097/20000, R3[396/800], Temp: 0.5079, Energy: -6.280217-0.001386j, Rel_err(%): 1.8104
[2025-06-05 12:58:03] Iter: 1098/20000, R3[397/800], Temp: 0.5059, Energy: -6.281361+0.000778j, Rel_err(%): 1.7924
[2025-06-05 12:58:06] Iter: 1099/20000, R3[398/800], Temp: 0.5039, Energy: -6.279912-0.001902j, Rel_err(%): 1.8153
[2025-06-05 12:58:09] Iter: 1100/20000, R3[399/800], Temp: 0.5020, Energy: -6.280409-0.001000j, Rel_err(%): 1.8073
[2025-06-05 12:58:13] Iter: 1101/20000, R3[400/800], Temp: 0.5000, Energy: -6.281324+0.000310j, Rel_err(%): 1.7929
[2025-06-05 12:58:16] Iter: 1102/20000, R3[401/800], Temp: 0.4980, Energy: -6.278188+0.000037j, Rel_err(%): 1.8420
[2025-06-05 12:58:19] Iter: 1103/20000, R3[402/800], Temp: 0.4961, Energy: -6.280224-0.000417j, Rel_err(%): 1.8101
[2025-06-05 12:58:22] Iter: 1104/20000, R3[403/800], Temp: 0.4941, Energy: -6.279549+0.001472j, Rel_err(%): 1.8208
[2025-06-05 12:58:25] Iter: 1105/20000, R3[404/800], Temp: 0.4921, Energy: -6.280065-0.000542j, Rel_err(%): 1.8126
[2025-06-05 12:58:28] Iter: 1106/20000, R3[405/800], Temp: 0.4902, Energy: -6.280852+0.000683j, Rel_err(%): 1.8003
[2025-06-05 12:58:32] Iter: 1107/20000, R3[406/800], Temp: 0.4882, Energy: -6.280411+0.000568j, Rel_err(%): 1.8072
[2025-06-05 12:58:35] Iter: 1108/20000, R3[407/800], Temp: 0.4863, Energy: -6.280217-0.000081j, Rel_err(%): 1.8102
[2025-06-05 12:58:38] Iter: 1109/20000, R3[408/800], Temp: 0.4843, Energy: -6.279437-0.000588j, Rel_err(%): 1.8225
[2025-06-05 12:58:41] Iter: 1110/20000, R3[409/800], Temp: 0.4823, Energy: -6.281033-0.000572j, Rel_err(%): 1.7975
[2025-06-05 12:58:44] Iter: 1111/20000, R3[410/800], Temp: 0.4804, Energy: -6.281625+0.000665j, Rel_err(%): 1.7883
[2025-06-05 12:58:47] Iter: 1112/20000, R3[411/800], Temp: 0.4784, Energy: -6.281511-0.000582j, Rel_err(%): 1.7900
[2025-06-05 12:58:51] Iter: 1113/20000, R3[412/800], Temp: 0.4764, Energy: -6.282635-0.000670j, Rel_err(%): 1.7725
[2025-06-05 12:58:54] Iter: 1114/20000, R3[413/800], Temp: 0.4745, Energy: -6.282295+0.001191j, Rel_err(%): 1.7779
[2025-06-05 12:58:57] Iter: 1115/20000, R3[414/800], Temp: 0.4725, Energy: -6.278719+0.000815j, Rel_err(%): 1.8337
[2025-06-05 12:59:00] Iter: 1116/20000, R3[415/800], Temp: 0.4706, Energy: -6.280355+0.000208j, Rel_err(%): 1.8081
[2025-06-05 12:59:03] Iter: 1117/20000, R3[416/800], Temp: 0.4686, Energy: -6.281310-0.000170j, Rel_err(%): 1.7931
[2025-06-05 12:59:06] Iter: 1118/20000, R3[417/800], Temp: 0.4666, Energy: -6.280680+0.000393j, Rel_err(%): 1.8030
[2025-06-05 12:59:10] Iter: 1119/20000, R3[418/800], Temp: 0.4647, Energy: -6.278747+0.000225j, Rel_err(%): 1.8332
[2025-06-05 12:59:13] Iter: 1120/20000, R3[419/800], Temp: 0.4627, Energy: -6.280633+0.000417j, Rel_err(%): 1.8037
[2025-06-05 12:59:16] Iter: 1121/20000, R3[420/800], Temp: 0.4608, Energy: -6.280828-0.000164j, Rel_err(%): 1.8007
[2025-06-05 12:59:19] Iter: 1122/20000, R3[421/800], Temp: 0.4588, Energy: -6.280056-0.001079j, Rel_err(%): 1.8128
[2025-06-05 12:59:22] Iter: 1123/20000, R3[422/800], Temp: 0.4569, Energy: -6.280321-0.001550j, Rel_err(%): 1.8088
[2025-06-05 12:59:25] Iter: 1124/20000, R3[423/800], Temp: 0.4549, Energy: -6.281027-0.001549j, Rel_err(%): 1.7977
[2025-06-05 12:59:29] Iter: 1125/20000, R3[424/800], Temp: 0.4529, Energy: -6.280220-0.000170j, Rel_err(%): 1.8102
[2025-06-05 12:59:32] Iter: 1126/20000, R3[425/800], Temp: 0.4510, Energy: -6.282351-0.000046j, Rel_err(%): 1.7769
[2025-06-05 12:59:35] Iter: 1127/20000, R3[426/800], Temp: 0.4490, Energy: -6.279191-0.000327j, Rel_err(%): 1.8263
[2025-06-05 12:59:38] Iter: 1128/20000, R3[427/800], Temp: 0.4471, Energy: -6.281771+0.000983j, Rel_err(%): 1.7860
[2025-06-05 12:59:41] Iter: 1129/20000, R3[428/800], Temp: 0.4451, Energy: -6.280184+0.001618j, Rel_err(%): 1.8109
[2025-06-05 12:59:44] Iter: 1130/20000, R3[429/800], Temp: 0.4432, Energy: -6.282187-0.001796j, Rel_err(%): 1.7797
[2025-06-05 12:59:48] Iter: 1131/20000, R3[430/800], Temp: 0.4412, Energy: -6.278756-0.000912j, Rel_err(%): 1.8331
[2025-06-05 12:59:51] Iter: 1132/20000, R3[431/800], Temp: 0.4393, Energy: -6.281021+0.000672j, Rel_err(%): 1.7977
[2025-06-05 12:59:54] Iter: 1133/20000, R3[432/800], Temp: 0.4373, Energy: -6.282158+0.000234j, Rel_err(%): 1.7799
[2025-06-05 12:59:57] Iter: 1134/20000, R3[433/800], Temp: 0.4354, Energy: -6.280704-0.001132j, Rel_err(%): 1.8027
[2025-06-05 13:00:00] Iter: 1135/20000, R3[434/800], Temp: 0.4334, Energy: -6.281482-0.000757j, Rel_err(%): 1.7905
[2025-06-05 13:00:03] Iter: 1136/20000, R3[435/800], Temp: 0.4315, Energy: -6.283530-0.000356j, Rel_err(%): 1.7584
[2025-06-05 13:00:07] Iter: 1137/20000, R3[436/800], Temp: 0.4295, Energy: -6.282814-0.000711j, Rel_err(%): 1.7697
[2025-06-05 13:00:10] Iter: 1138/20000, R3[437/800], Temp: 0.4276, Energy: -6.280535-0.000979j, Rel_err(%): 1.8053
[2025-06-05 13:00:13] Iter: 1139/20000, R3[438/800], Temp: 0.4257, Energy: -6.280674-0.001377j, Rel_err(%): 1.8032
[2025-06-05 13:00:16] Iter: 1140/20000, R3[439/800], Temp: 0.4237, Energy: -6.279612-0.000440j, Rel_err(%): 1.8197
[2025-06-05 13:00:19] Iter: 1141/20000, R3[440/800], Temp: 0.4218, Energy: -6.280298+0.000169j, Rel_err(%): 1.8090
[2025-06-05 13:00:23] Iter: 1142/20000, R3[441/800], Temp: 0.4198, Energy: -6.278663-0.001103j, Rel_err(%): 1.8346
[2025-06-05 13:00:26] Iter: 1143/20000, R3[442/800], Temp: 0.4179, Energy: -6.279373-0.002153j, Rel_err(%): 1.8238
[2025-06-05 13:00:29] Iter: 1144/20000, R3[443/800], Temp: 0.4160, Energy: -6.280687-0.002779j, Rel_err(%): 1.8034
[2025-06-05 13:00:32] Iter: 1145/20000, R3[444/800], Temp: 0.4140, Energy: -6.278448-0.000574j, Rel_err(%): 1.8379
[2025-06-05 13:00:35] Iter: 1146/20000, R3[445/800], Temp: 0.4121, Energy: -6.279887-0.000459j, Rel_err(%): 1.8154
[2025-06-05 13:00:38] Iter: 1147/20000, R3[446/800], Temp: 0.4102, Energy: -6.280071-0.000452j, Rel_err(%): 1.8125
[2025-06-05 13:00:41] Iter: 1148/20000, R3[447/800], Temp: 0.4082, Energy: -6.281156-0.000268j, Rel_err(%): 1.7956
[2025-06-05 13:00:45] Iter: 1149/20000, R3[448/800], Temp: 0.4063, Energy: -6.279189+0.000092j, Rel_err(%): 1.8263
[2025-06-05 13:00:48] Iter: 1150/20000, R3[449/800], Temp: 0.4044, Energy: -6.281072+0.002413j, Rel_err(%): 1.7973
[2025-06-05 13:00:51] Iter: 1151/20000, R3[450/800], Temp: 0.4025, Energy: -6.281511-0.000092j, Rel_err(%): 1.7900
[2025-06-05 13:00:54] Iter: 1152/20000, R3[451/800], Temp: 0.4005, Energy: -6.278544-0.000329j, Rel_err(%): 1.8364
[2025-06-05 13:00:57] Iter: 1153/20000, R3[452/800], Temp: 0.3986, Energy: -6.279951-0.000725j, Rel_err(%): 1.8144
[2025-06-05 13:01:00] Iter: 1154/20000, R3[453/800], Temp: 0.3967, Energy: -6.279095+0.001746j, Rel_err(%): 1.8280
[2025-06-05 13:01:04] Iter: 1155/20000, R3[454/800], Temp: 0.3948, Energy: -6.280325+0.003043j, Rel_err(%): 1.8092
[2025-06-05 13:01:07] Iter: 1156/20000, R3[455/800], Temp: 0.3928, Energy: -6.278423-0.000491j, Rel_err(%): 1.8383
[2025-06-05 13:01:10] Iter: 1157/20000, R3[456/800], Temp: 0.3909, Energy: -6.278497-0.000546j, Rel_err(%): 1.8372
[2025-06-05 13:01:13] Iter: 1158/20000, R3[457/800], Temp: 0.3890, Energy: -6.279490+0.000672j, Rel_err(%): 1.8216
[2025-06-05 13:01:16] Iter: 1159/20000, R3[458/800], Temp: 0.3871, Energy: -6.278354+0.000128j, Rel_err(%): 1.8394
[2025-06-05 13:01:19] Iter: 1160/20000, R3[459/800], Temp: 0.3852, Energy: -6.280693-0.001215j, Rel_err(%): 1.8029
[2025-06-05 13:01:23] Iter: 1161/20000, R3[460/800], Temp: 0.3833, Energy: -6.279004+0.000941j, Rel_err(%): 1.8293
[2025-06-05 13:01:26] Iter: 1162/20000, R3[461/800], Temp: 0.3814, Energy: -6.280858+0.002577j, Rel_err(%): 1.8007
[2025-06-05 13:01:29] Iter: 1163/20000, R3[462/800], Temp: 0.3795, Energy: -6.280798+0.001037j, Rel_err(%): 1.8012
[2025-06-05 13:01:32] Iter: 1164/20000, R3[463/800], Temp: 0.3776, Energy: -6.279691-0.000154j, Rel_err(%): 1.8185
[2025-06-05 13:01:35] Iter: 1165/20000, R3[464/800], Temp: 0.3757, Energy: -6.281193-0.000770j, Rel_err(%): 1.7950
[2025-06-05 13:01:38] Iter: 1166/20000, R3[465/800], Temp: 0.3738, Energy: -6.279697-0.000663j, Rel_err(%): 1.8184
[2025-06-05 13:01:42] Iter: 1167/20000, R3[466/800], Temp: 0.3719, Energy: -6.279701+0.000821j, Rel_err(%): 1.8184
[2025-06-05 13:01:45] Iter: 1168/20000, R3[467/800], Temp: 0.3700, Energy: -6.279122+0.000710j, Rel_err(%): 1.8274
[2025-06-05 13:01:48] Iter: 1169/20000, R3[468/800], Temp: 0.3681, Energy: -6.280474+0.000036j, Rel_err(%): 1.8062
[2025-06-05 13:01:51] Iter: 1170/20000, R3[469/800], Temp: 0.3662, Energy: -6.280302+0.001327j, Rel_err(%): 1.8090
[2025-06-05 13:01:54] Iter: 1171/20000, R3[470/800], Temp: 0.3643, Energy: -6.277953-0.000186j, Rel_err(%): 1.8456
[2025-06-05 13:01:57] Iter: 1172/20000, R3[471/800], Temp: 0.3624, Energy: -6.281974-0.001672j, Rel_err(%): 1.7830
[2025-06-05 13:02:00] Iter: 1173/20000, R3[472/800], Temp: 0.3605, Energy: -6.281380+0.000139j, Rel_err(%): 1.7921
[2025-06-05 13:02:04] Iter: 1174/20000, R3[473/800], Temp: 0.3586, Energy: -6.282065-0.000704j, Rel_err(%): 1.7814
[2025-06-05 13:02:07] Iter: 1175/20000, R3[474/800], Temp: 0.3567, Energy: -6.280110+0.000214j, Rel_err(%): 1.8119
[2025-06-05 13:02:10] Iter: 1176/20000, R3[475/800], Temp: 0.3549, Energy: -6.283052-0.001250j, Rel_err(%): 1.7660
[2025-06-05 13:02:13] Iter: 1177/20000, R3[476/800], Temp: 0.3530, Energy: -6.281938+0.000040j, Rel_err(%): 1.7833
[2025-06-05 13:02:16] Iter: 1178/20000, R3[477/800], Temp: 0.3511, Energy: -6.279766-0.000172j, Rel_err(%): 1.8173
[2025-06-05 13:02:19] Iter: 1179/20000, R3[478/800], Temp: 0.3492, Energy: -6.282659+0.001855j, Rel_err(%): 1.7723
[2025-06-05 13:02:23] Iter: 1180/20000, R3[479/800], Temp: 0.3474, Energy: -6.280746-0.000854j, Rel_err(%): 1.8020
[2025-06-05 13:02:26] Iter: 1181/20000, R3[480/800], Temp: 0.3455, Energy: -6.280075-0.000478j, Rel_err(%): 1.8125
[2025-06-05 13:02:29] Iter: 1182/20000, R3[481/800], Temp: 0.3436, Energy: -6.280271+0.000045j, Rel_err(%): 1.8094
[2025-06-05 13:02:32] Iter: 1183/20000, R3[482/800], Temp: 0.3418, Energy: -6.281120-0.000717j, Rel_err(%): 1.7962
[2025-06-05 13:02:35] Iter: 1184/20000, R3[483/800], Temp: 0.3399, Energy: -6.280188+0.001313j, Rel_err(%): 1.8108
[2025-06-05 13:02:38] Iter: 1185/20000, R3[484/800], Temp: 0.3380, Energy: -6.281805+0.000593j, Rel_err(%): 1.7854
[2025-06-05 13:02:42] Iter: 1186/20000, R3[485/800], Temp: 0.3362, Energy: -6.282106-0.001100j, Rel_err(%): 1.7808
[2025-06-05 13:02:45] Iter: 1187/20000, R3[486/800], Temp: 0.3343, Energy: -6.282585-0.000819j, Rel_err(%): 1.7733
[2025-06-05 13:02:48] Iter: 1188/20000, R3[487/800], Temp: 0.3325, Energy: -6.281844+0.000407j, Rel_err(%): 1.7848
[2025-06-05 13:02:51] Iter: 1189/20000, R3[488/800], Temp: 0.3306, Energy: -6.284021-0.000765j, Rel_err(%): 1.7508
[2025-06-05 13:02:54] Iter: 1190/20000, R3[489/800], Temp: 0.3288, Energy: -6.282031+0.000403j, Rel_err(%): 1.7819
[2025-06-05 13:02:57] Iter: 1191/20000, R3[490/800], Temp: 0.3269, Energy: -6.281298-0.000270j, Rel_err(%): 1.7933
[2025-06-05 13:03:00] Iter: 1192/20000, R3[491/800], Temp: 0.3251, Energy: -6.279855+0.001573j, Rel_err(%): 1.8161
[2025-06-05 13:03:04] Iter: 1193/20000, R3[492/800], Temp: 0.3233, Energy: -6.279708-0.000999j, Rel_err(%): 1.8183
[2025-06-05 13:03:07] Iter: 1194/20000, R3[493/800], Temp: 0.3214, Energy: -6.279990+0.000611j, Rel_err(%): 1.8138
[2025-06-05 13:03:10] Iter: 1195/20000, R3[494/800], Temp: 0.3196, Energy: -6.282204-0.000670j, Rel_err(%): 1.7792
[2025-06-05 13:03:13] Iter: 1196/20000, R3[495/800], Temp: 0.3178, Energy: -6.278897+0.001187j, Rel_err(%): 1.8310
[2025-06-05 13:03:16] Iter: 1197/20000, R3[496/800], Temp: 0.3159, Energy: -6.280300-0.000300j, Rel_err(%): 1.8089
[2025-06-05 13:03:19] Iter: 1198/20000, R3[497/800], Temp: 0.3141, Energy: -6.280320-0.000252j, Rel_err(%): 1.8086
[2025-06-05 13:03:23] Iter: 1199/20000, R3[498/800], Temp: 0.3123, Energy: -6.279791-0.000655j, Rel_err(%): 1.8169
[2025-06-05 13:03:26] Iter: 1200/20000, R3[499/800], Temp: 0.3105, Energy: -6.279601-0.000700j, Rel_err(%): 1.8199
[2025-06-05 13:03:29] Iter: 1201/20000, R3[500/800], Temp: 0.3087, Energy: -6.281093-0.000878j, Rel_err(%): 1.7966
[2025-06-05 13:03:32] Iter: 1202/20000, R3[501/800], Temp: 0.3068, Energy: -6.278055-0.000436j, Rel_err(%): 1.8441
[2025-06-05 13:03:35] Iter: 1203/20000, R3[502/800], Temp: 0.3050, Energy: -6.281367+0.000408j, Rel_err(%): 1.7923
[2025-06-05 13:03:38] Iter: 1204/20000, R3[503/800], Temp: 0.3032, Energy: -6.280524-0.001308j, Rel_err(%): 1.8056
[2025-06-05 13:03:41] Iter: 1205/20000, R3[504/800], Temp: 0.3014, Energy: -6.280565+0.003185j, Rel_err(%): 1.8055
[2025-06-05 13:03:45] Iter: 1206/20000, R3[505/800], Temp: 0.2996, Energy: -6.280722-0.000272j, Rel_err(%): 1.8024
[2025-06-05 13:03:48] Iter: 1207/20000, R3[506/800], Temp: 0.2978, Energy: -6.278576-0.000234j, Rel_err(%): 1.8359
[2025-06-05 13:03:51] Iter: 1208/20000, R3[507/800], Temp: 0.2960, Energy: -6.280922+0.000446j, Rel_err(%): 1.7992
[2025-06-05 13:03:54] Iter: 1209/20000, R3[508/800], Temp: 0.2942, Energy: -6.281552-0.000740j, Rel_err(%): 1.7894
[2025-06-05 13:03:57] Iter: 1210/20000, R3[509/800], Temp: 0.2925, Energy: -6.279310+0.000403j, Rel_err(%): 1.8244
[2025-06-05 13:04:00] Iter: 1211/20000, R3[510/800], Temp: 0.2907, Energy: -6.279979+0.000272j, Rel_err(%): 1.8140
[2025-06-05 13:04:04] Iter: 1212/20000, R3[511/800], Temp: 0.2889, Energy: -6.282728-0.001575j, Rel_err(%): 1.7712
[2025-06-05 13:04:07] Iter: 1213/20000, R3[512/800], Temp: 0.2871, Energy: -6.280208+0.000906j, Rel_err(%): 1.8104
[2025-06-05 13:04:10] Iter: 1214/20000, R3[513/800], Temp: 0.2853, Energy: -6.281040+0.000426j, Rel_err(%): 1.7974
[2025-06-05 13:04:13] Iter: 1215/20000, R3[514/800], Temp: 0.2836, Energy: -6.280702-0.000634j, Rel_err(%): 1.8027
[2025-06-05 13:04:16] Iter: 1216/20000, R3[515/800], Temp: 0.2818, Energy: -6.281659-0.001044j, Rel_err(%): 1.7878
[2025-06-05 13:04:19] Iter: 1217/20000, R3[516/800], Temp: 0.2800, Energy: -6.281795-0.000013j, Rel_err(%): 1.7856
[2025-06-05 13:04:23] Iter: 1218/20000, R3[517/800], Temp: 0.2783, Energy: -6.282492-0.001206j, Rel_err(%): 1.7748
[2025-06-05 13:04:26] Iter: 1219/20000, R3[518/800], Temp: 0.2765, Energy: -6.279862+0.000175j, Rel_err(%): 1.8158
[2025-06-05 13:04:29] Iter: 1220/20000, R3[519/800], Temp: 0.2748, Energy: -6.278986+0.000457j, Rel_err(%): 1.8295
[2025-06-05 13:04:32] Iter: 1221/20000, R3[520/800], Temp: 0.2730, Energy: -6.280217+0.000761j, Rel_err(%): 1.8103
[2025-06-05 13:04:35] Iter: 1222/20000, R3[521/800], Temp: 0.2713, Energy: -6.279895+0.000891j, Rel_err(%): 1.8153
[2025-06-05 13:04:38] Iter: 1223/20000, R3[522/800], Temp: 0.2695, Energy: -6.281136+0.001262j, Rel_err(%): 1.7960
[2025-06-05 13:04:42] Iter: 1224/20000, R3[523/800], Temp: 0.2678, Energy: -6.280131-0.000601j, Rel_err(%): 1.8116
[2025-06-05 13:04:45] Iter: 1225/20000, R3[524/800], Temp: 0.2660, Energy: -6.280434-0.001057j, Rel_err(%): 1.8069
[2025-06-05 13:04:48] Iter: 1226/20000, R3[525/800], Temp: 0.2643, Energy: -6.279551-0.000655j, Rel_err(%): 1.8207
[2025-06-05 13:04:51] Iter: 1227/20000, R3[526/800], Temp: 0.2626, Energy: -6.281374-0.000061j, Rel_err(%): 1.7922
[2025-06-05 13:04:54] Iter: 1228/20000, R3[527/800], Temp: 0.2608, Energy: -6.280990-0.000037j, Rel_err(%): 1.7982
[2025-06-05 13:04:57] Iter: 1229/20000, R3[528/800], Temp: 0.2591, Energy: -6.279476+0.001820j, Rel_err(%): 1.8220
[2025-06-05 13:05:00] Iter: 1230/20000, R3[529/800], Temp: 0.2574, Energy: -6.282208-0.000732j, Rel_err(%): 1.7791
[2025-06-05 13:05:04] Iter: 1231/20000, R3[530/800], Temp: 0.2557, Energy: -6.280762-0.000950j, Rel_err(%): 1.8018
[2025-06-05 13:05:07] Iter: 1232/20000, R3[531/800], Temp: 0.2540, Energy: -6.280082-0.000647j, Rel_err(%): 1.8124
[2025-06-05 13:05:10] Iter: 1233/20000, R3[532/800], Temp: 0.2523, Energy: -6.279494-0.000154j, Rel_err(%): 1.8215
[2025-06-05 13:05:13] Iter: 1234/20000, R3[533/800], Temp: 0.2506, Energy: -6.280468+0.002169j, Rel_err(%): 1.8066
[2025-06-05 13:05:16] Iter: 1235/20000, R3[534/800], Temp: 0.2489, Energy: -6.280531-0.000930j, Rel_err(%): 1.8054
[2025-06-05 13:05:19] Iter: 1236/20000, R3[535/800], Temp: 0.2472, Energy: -6.282331-0.000860j, Rel_err(%): 1.7772
[2025-06-05 13:05:23] Iter: 1237/20000, R3[536/800], Temp: 0.2455, Energy: -6.281423-0.000424j, Rel_err(%): 1.7914
[2025-06-05 13:05:26] Iter: 1238/20000, R3[537/800], Temp: 0.2438, Energy: -6.281908+0.000963j, Rel_err(%): 1.7839
[2025-06-05 13:05:29] Iter: 1239/20000, R3[538/800], Temp: 0.2421, Energy: -6.281426-0.000197j, Rel_err(%): 1.7913
[2025-06-05 13:05:32] Iter: 1240/20000, R3[539/800], Temp: 0.2404, Energy: -6.282815-0.000180j, Rel_err(%): 1.7696
[2025-06-05 13:05:35] Iter: 1241/20000, R3[540/800], Temp: 0.2388, Energy: -6.281765+0.001635j, Rel_err(%): 1.7862
[2025-06-05 13:05:38] Iter: 1242/20000, R3[541/800], Temp: 0.2371, Energy: -6.282388+0.000797j, Rel_err(%): 1.7763
[2025-06-05 13:05:41] Iter: 1243/20000, R3[542/800], Temp: 0.2354, Energy: -6.283817+0.001156j, Rel_err(%): 1.7540
[2025-06-05 13:05:45] Iter: 1244/20000, R3[543/800], Temp: 0.2337, Energy: -6.280889+0.000473j, Rel_err(%): 1.7997
[2025-06-05 13:05:48] Iter: 1245/20000, R3[544/800], Temp: 0.2321, Energy: -6.283206-0.001287j, Rel_err(%): 1.7636
[2025-06-05 13:05:51] Iter: 1246/20000, R3[545/800], Temp: 0.2304, Energy: -6.282708+0.002583j, Rel_err(%): 1.7718
[2025-06-05 13:05:54] Iter: 1247/20000, R3[546/800], Temp: 0.2288, Energy: -6.281928+0.001001j, Rel_err(%): 1.7836
[2025-06-05 13:05:57] Iter: 1248/20000, R3[547/800], Temp: 0.2271, Energy: -6.280204-0.000093j, Rel_err(%): 1.8104
[2025-06-05 13:06:00] Iter: 1249/20000, R3[548/800], Temp: 0.2255, Energy: -6.279925-0.000136j, Rel_err(%): 1.8148
[2025-06-05 13:06:04] Iter: 1250/20000, R3[549/800], Temp: 0.2238, Energy: -6.281238+0.000950j, Rel_err(%): 1.7943
[2025-06-05 13:06:07] Iter: 1251/20000, R3[550/800], Temp: 0.2222, Energy: -6.281603+0.000231j, Rel_err(%): 1.7886
[2025-06-05 13:06:10] Iter: 1252/20000, R3[551/800], Temp: 0.2206, Energy: -6.280373+0.000649j, Rel_err(%): 1.8078
[2025-06-05 13:06:13] Iter: 1253/20000, R3[552/800], Temp: 0.2190, Energy: -6.281206-0.001905j, Rel_err(%): 1.7950
[2025-06-05 13:06:16] Iter: 1254/20000, R3[553/800], Temp: 0.2173, Energy: -6.281437+0.000978j, Rel_err(%): 1.7912
[2025-06-05 13:06:19] Iter: 1255/20000, R3[554/800], Temp: 0.2157, Energy: -6.283193+0.000177j, Rel_err(%): 1.7637
[2025-06-05 13:06:23] Iter: 1256/20000, R3[555/800], Temp: 0.2141, Energy: -6.279805-0.000177j, Rel_err(%): 1.8167
[2025-06-05 13:06:26] Iter: 1257/20000, R3[556/800], Temp: 0.2125, Energy: -6.281881+0.000108j, Rel_err(%): 1.7842
[2025-06-05 13:06:29] Iter: 1258/20000, R3[557/800], Temp: 0.2109, Energy: -6.281866+0.000244j, Rel_err(%): 1.7845
[2025-06-05 13:06:32] Iter: 1259/20000, R3[558/800], Temp: 0.2093, Energy: -6.281972-0.001740j, Rel_err(%): 1.7830
[2025-06-05 13:06:35] Iter: 1260/20000, R3[559/800], Temp: 0.2077, Energy: -6.282156+0.000403j, Rel_err(%): 1.7799
[2025-06-05 13:06:38] Iter: 1261/20000, R3[560/800], Temp: 0.2061, Energy: -6.281613-0.000473j, Rel_err(%): 1.7884
[2025-06-05 13:06:42] Iter: 1262/20000, R3[561/800], Temp: 0.2045, Energy: -6.280193+0.002285j, Rel_err(%): 1.8110
[2025-06-05 13:06:45] Iter: 1263/20000, R3[562/800], Temp: 0.2029, Energy: -6.281526-0.001717j, Rel_err(%): 1.7900
[2025-06-05 13:06:48] Iter: 1264/20000, R3[563/800], Temp: 0.2014, Energy: -6.282312+0.000388j, Rel_err(%): 1.7775
[2025-06-05 13:06:51] Iter: 1265/20000, R3[564/800], Temp: 0.1998, Energy: -6.281494-0.000256j, Rel_err(%): 1.7903
[2025-06-05 13:06:54] Iter: 1266/20000, R3[565/800], Temp: 0.1982, Energy: -6.281899+0.000795j, Rel_err(%): 1.7840
[2025-06-05 13:06:57] Iter: 1267/20000, R3[566/800], Temp: 0.1967, Energy: -6.281941-0.000146j, Rel_err(%): 1.7833
[2025-06-05 13:07:00] Iter: 1268/20000, R3[567/800], Temp: 0.1951, Energy: -6.280836+0.000859j, Rel_err(%): 1.8006
[2025-06-05 13:07:04] Iter: 1269/20000, R3[568/800], Temp: 0.1935, Energy: -6.282515-0.000856j, Rel_err(%): 1.7744
[2025-06-05 13:07:07] Iter: 1270/20000, R3[569/800], Temp: 0.1920, Energy: -6.278405+0.000315j, Rel_err(%): 1.8386
[2025-06-05 13:07:10] Iter: 1271/20000, R3[570/800], Temp: 0.1905, Energy: -6.282490-0.000533j, Rel_err(%): 1.7747
[2025-06-05 13:07:13] Iter: 1272/20000, R3[571/800], Temp: 0.1889, Energy: -6.280638-0.000267j, Rel_err(%): 1.8037
[2025-06-05 13:07:16] Iter: 1273/20000, R3[572/800], Temp: 0.1874, Energy: -6.280057+0.001818j, Rel_err(%): 1.8130
[2025-06-05 13:07:19] Iter: 1274/20000, R3[573/800], Temp: 0.1858, Energy: -6.281396-0.000154j, Rel_err(%): 1.7918
[2025-06-05 13:07:23] Iter: 1275/20000, R3[574/800], Temp: 0.1843, Energy: -6.282869-0.000607j, Rel_err(%): 1.7688
[2025-06-05 13:07:26] Iter: 1276/20000, R3[575/800], Temp: 0.1828, Energy: -6.279951-0.000841j, Rel_err(%): 1.8144
[2025-06-05 13:07:29] Iter: 1277/20000, R3[576/800], Temp: 0.1813, Energy: -6.282314-0.000513j, Rel_err(%): 1.7775
[2025-06-05 13:07:32] Iter: 1278/20000, R3[577/800], Temp: 0.1798, Energy: -6.282386-0.001132j, Rel_err(%): 1.7764
[2025-06-05 13:07:35] Iter: 1279/20000, R3[578/800], Temp: 0.1783, Energy: -6.281513-0.001697j, Rel_err(%): 1.7902
[2025-06-05 13:07:38] Iter: 1280/20000, R3[579/800], Temp: 0.1768, Energy: -6.279849+0.000176j, Rel_err(%): 1.8160
[2025-06-05 13:07:41] Iter: 1281/20000, R3[580/800], Temp: 0.1753, Energy: -6.278076-0.002137j, Rel_err(%): 1.8440
[2025-06-05 13:07:45] Iter: 1282/20000, R3[581/800], Temp: 0.1738, Energy: -6.279472-0.000070j, Rel_err(%): 1.8219
[2025-06-05 13:07:48] Iter: 1283/20000, R3[582/800], Temp: 0.1723, Energy: -6.280635-0.000288j, Rel_err(%): 1.8037
[2025-06-05 13:07:51] Iter: 1284/20000, R3[583/800], Temp: 0.1708, Energy: -6.281999+0.000224j, Rel_err(%): 1.7824
[2025-06-05 13:07:54] Iter: 1285/20000, R3[584/800], Temp: 0.1693, Energy: -6.280467+0.001165j, Rel_err(%): 1.8064
[2025-06-05 13:07:57] Iter: 1286/20000, R3[585/800], Temp: 0.1679, Energy: -6.280476-0.000240j, Rel_err(%): 1.8062
[2025-06-05 13:08:00] Iter: 1287/20000, R3[586/800], Temp: 0.1664, Energy: -6.282264-0.000696j, Rel_err(%): 1.7783
[2025-06-05 13:08:04] Iter: 1288/20000, R3[587/800], Temp: 0.1649, Energy: -6.281552+0.000139j, Rel_err(%): 1.7894
[2025-06-05 13:08:07] Iter: 1289/20000, R3[588/800], Temp: 0.1635, Energy: -6.281887+0.000234j, Rel_err(%): 1.7841
[2025-06-05 13:08:10] Iter: 1290/20000, R3[589/800], Temp: 0.1620, Energy: -6.281484-0.001050j, Rel_err(%): 1.7905
[2025-06-05 13:08:13] Iter: 1291/20000, R3[590/800], Temp: 0.1606, Energy: -6.280945+0.000021j, Rel_err(%): 1.7989
[2025-06-05 13:08:16] Iter: 1292/20000, R3[591/800], Temp: 0.1592, Energy: -6.279462-0.000698j, Rel_err(%): 1.8221
[2025-06-05 13:08:19] Iter: 1293/20000, R3[592/800], Temp: 0.1577, Energy: -6.280496-0.000992j, Rel_err(%): 1.8059
[2025-06-05 13:08:23] Iter: 1294/20000, R3[593/800], Temp: 0.1563, Energy: -6.280783-0.000902j, Rel_err(%): 1.8015
[2025-06-05 13:08:26] Iter: 1295/20000, R3[594/800], Temp: 0.1549, Energy: -6.279269+0.000321j, Rel_err(%): 1.8251
[2025-06-05 13:08:29] Iter: 1296/20000, R3[595/800], Temp: 0.1535, Energy: -6.279865-0.000620j, Rel_err(%): 1.8158
[2025-06-05 13:08:32] Iter: 1297/20000, R3[596/800], Temp: 0.1520, Energy: -6.279471-0.000935j, Rel_err(%): 1.8220
[2025-06-05 13:08:35] Iter: 1298/20000, R3[597/800], Temp: 0.1506, Energy: -6.280678-0.000200j, Rel_err(%): 1.8030
[2025-06-05 13:08:38] Iter: 1299/20000, R3[598/800], Temp: 0.1492, Energy: -6.280341+0.000077j, Rel_err(%): 1.8083
[2025-06-05 13:08:42] Iter: 1300/20000, R3[599/800], Temp: 0.1478, Energy: -6.281039+0.000325j, Rel_err(%): 1.7974
[2025-06-05 13:08:45] Iter: 1301/20000, R3[600/800], Temp: 0.1464, Energy: -6.280419+0.002591j, Rel_err(%): 1.8075
[2025-06-05 13:08:48] Iter: 1302/20000, R3[601/800], Temp: 0.1451, Energy: -6.282079-0.000999j, Rel_err(%): 1.7812
[2025-06-05 13:08:51] Iter: 1303/20000, R3[602/800], Temp: 0.1437, Energy: -6.280561-0.001396j, Rel_err(%): 1.8050
[2025-06-05 13:08:54] Iter: 1304/20000, R3[603/800], Temp: 0.1423, Energy: -6.281083+0.001561j, Rel_err(%): 1.7969
[2025-06-05 13:08:57] Iter: 1305/20000, R3[604/800], Temp: 0.1409, Energy: -6.282530+0.000519j, Rel_err(%): 1.7741
[2025-06-05 13:09:00] Iter: 1306/20000, R3[605/800], Temp: 0.1396, Energy: -6.280933+0.001157j, Rel_err(%): 1.7991
[2025-06-05 13:09:04] Iter: 1307/20000, R3[606/800], Temp: 0.1382, Energy: -6.282214+0.000735j, Rel_err(%): 1.7791
[2025-06-05 13:09:07] Iter: 1308/20000, R3[607/800], Temp: 0.1369, Energy: -6.282026+0.000298j, Rel_err(%): 1.7820
[2025-06-05 13:09:10] Iter: 1309/20000, R3[608/800], Temp: 0.1355, Energy: -6.281847+0.000222j, Rel_err(%): 1.7848
[2025-06-05 13:09:13] Iter: 1310/20000, R3[609/800], Temp: 0.1342, Energy: -6.281283+0.000562j, Rel_err(%): 1.7936
[2025-06-05 13:09:16] Iter: 1311/20000, R3[610/800], Temp: 0.1328, Energy: -6.281560+0.001601j, Rel_err(%): 1.7894
[2025-06-05 13:09:19] Iter: 1312/20000, R3[611/800], Temp: 0.1315, Energy: -6.282465+0.000372j, Rel_err(%): 1.7751
[2025-06-05 13:09:23] Iter: 1313/20000, R3[612/800], Temp: 0.1302, Energy: -6.280950+0.000651j, Rel_err(%): 1.7988
[2025-06-05 13:09:26] Iter: 1314/20000, R3[613/800], Temp: 0.1289, Energy: -6.284242+0.001424j, Rel_err(%): 1.7475
[2025-06-05 13:09:29] Iter: 1315/20000, R3[614/800], Temp: 0.1276, Energy: -6.281751+0.001370j, Rel_err(%): 1.7864
[2025-06-05 13:09:32] Iter: 1316/20000, R3[615/800], Temp: 0.1262, Energy: -6.281677+0.000679j, Rel_err(%): 1.7874
[2025-06-05 13:09:35] Iter: 1317/20000, R3[616/800], Temp: 0.1249, Energy: -6.281729+0.000719j, Rel_err(%): 1.7866
[2025-06-05 13:09:38] Iter: 1318/20000, R3[617/800], Temp: 0.1236, Energy: -6.282037-0.000250j, Rel_err(%): 1.7818
[2025-06-05 13:09:42] Iter: 1319/20000, R3[618/800], Temp: 0.1224, Energy: -6.279902-0.000184j, Rel_err(%): 1.8152
[2025-06-05 13:09:45] Iter: 1320/20000, R3[619/800], Temp: 0.1211, Energy: -6.281624-0.000467j, Rel_err(%): 1.7883
[2025-06-05 13:09:48] Iter: 1321/20000, R3[620/800], Temp: 0.1198, Energy: -6.281254-0.001023j, Rel_err(%): 1.7941
[2025-06-05 13:09:51] Iter: 1322/20000, R3[621/800], Temp: 0.1185, Energy: -6.282823-0.000579j, Rel_err(%): 1.7695
[2025-06-05 13:09:54] Iter: 1323/20000, R3[622/800], Temp: 0.1173, Energy: -6.280754-0.000023j, Rel_err(%): 1.8018
[2025-06-05 13:09:57] Iter: 1324/20000, R3[623/800], Temp: 0.1160, Energy: -6.280603-0.001880j, Rel_err(%): 1.8044
[2025-06-05 13:10:00] Iter: 1325/20000, R3[624/800], Temp: 0.1147, Energy: -6.282123+0.000509j, Rel_err(%): 1.7805
[2025-06-05 13:10:04] Iter: 1326/20000, R3[625/800], Temp: 0.1135, Energy: -6.282836+0.000888j, Rel_err(%): 1.7693
[2025-06-05 13:10:07] Iter: 1327/20000, R3[626/800], Temp: 0.1123, Energy: -6.283066-0.000161j, Rel_err(%): 1.7657
[2025-06-05 13:10:10] Iter: 1328/20000, R3[627/800], Temp: 0.1110, Energy: -6.281059+0.000537j, Rel_err(%): 1.7971
[2025-06-05 13:10:13] Iter: 1329/20000, R3[628/800], Temp: 0.1098, Energy: -6.280952-0.000456j, Rel_err(%): 1.7988
[2025-06-05 13:10:16] Iter: 1330/20000, R3[629/800], Temp: 0.1086, Energy: -6.281821+0.000108j, Rel_err(%): 1.7852
[2025-06-05 13:10:19] Iter: 1331/20000, R3[630/800], Temp: 0.1073, Energy: -6.280492+0.000050j, Rel_err(%): 1.8059
[2025-06-05 13:10:23] Iter: 1332/20000, R3[631/800], Temp: 0.1061, Energy: -6.281559-0.001189j, Rel_err(%): 1.7893
[2025-06-05 13:10:26] Iter: 1333/20000, R3[632/800], Temp: 0.1049, Energy: -6.282729-0.000223j, Rel_err(%): 1.7710
[2025-06-05 13:10:29] Iter: 1334/20000, R3[633/800], Temp: 0.1037, Energy: -6.282358-0.001475j, Rel_err(%): 1.7769
[2025-06-05 13:10:32] Iter: 1335/20000, R3[634/800], Temp: 0.1025, Energy: -6.280220-0.001100j, Rel_err(%): 1.8103
[2025-06-05 13:10:35] Iter: 1336/20000, R3[635/800], Temp: 0.1013, Energy: -6.282429-0.000959j, Rel_err(%): 1.7757
[2025-06-05 13:10:38] Iter: 1337/20000, R3[636/800], Temp: 0.1002, Energy: -6.281914-0.001319j, Rel_err(%): 1.7838
[2025-06-05 13:10:42] Iter: 1338/20000, R3[637/800], Temp: 0.0990, Energy: -6.280754-0.002378j, Rel_err(%): 1.8022
[2025-06-05 13:10:45] Iter: 1339/20000, R3[638/800], Temp: 0.0978, Energy: -6.281959-0.001117j, Rel_err(%): 1.7831
[2025-06-05 13:10:48] Iter: 1340/20000, R3[639/800], Temp: 0.0966, Energy: -6.279335+0.000067j, Rel_err(%): 1.8240
[2025-06-05 13:10:51] Iter: 1341/20000, R3[640/800], Temp: 0.0955, Energy: -6.282148+0.000753j, Rel_err(%): 1.7801
[2025-06-05 13:10:54] Iter: 1342/20000, R3[641/800], Temp: 0.0943, Energy: -6.282409+0.000409j, Rel_err(%): 1.7760
[2025-06-05 13:10:57] Iter: 1343/20000, R3[642/800], Temp: 0.0932, Energy: -6.280598-0.002402j, Rel_err(%): 1.8047
[2025-06-05 13:11:00] Iter: 1344/20000, R3[643/800], Temp: 0.0921, Energy: -6.280760-0.001374j, Rel_err(%): 1.8019
[2025-06-05 13:11:04] Iter: 1345/20000, R3[644/800], Temp: 0.0909, Energy: -6.282304-0.000979j, Rel_err(%): 1.7777
[2025-06-05 13:11:07] Iter: 1346/20000, R3[645/800], Temp: 0.0898, Energy: -6.282048+0.000291j, Rel_err(%): 1.7816
[2025-06-05 13:11:10] Iter: 1347/20000, R3[646/800], Temp: 0.0887, Energy: -6.281025-0.001013j, Rel_err(%): 1.7977
[2025-06-05 13:11:13] Iter: 1348/20000, R3[647/800], Temp: 0.0876, Energy: -6.281533-0.000682j, Rel_err(%): 1.7897
[2025-06-05 13:11:16] Iter: 1349/20000, R3[648/800], Temp: 0.0865, Energy: -6.280455-0.000560j, Rel_err(%): 1.8065
[2025-06-05 13:11:19] Iter: 1350/20000, R3[649/800], Temp: 0.0854, Energy: -6.280135-0.001317j, Rel_err(%): 1.8116
[2025-06-05 13:11:23] Iter: 1351/20000, R3[650/800], Temp: 0.0843, Energy: -6.282604-0.000553j, Rel_err(%): 1.7729
[2025-06-05 13:11:26] Iter: 1352/20000, R3[651/800], Temp: 0.0832, Energy: -6.283158-0.000364j, Rel_err(%): 1.7643
[2025-06-05 13:11:29] Iter: 1353/20000, R3[652/800], Temp: 0.0821, Energy: -6.281678-0.000192j, Rel_err(%): 1.7874
[2025-06-05 13:11:32] Iter: 1354/20000, R3[653/800], Temp: 0.0810, Energy: -6.281265-0.000507j, Rel_err(%): 1.7939
[2025-06-05 13:11:35] Iter: 1355/20000, R3[654/800], Temp: 0.0800, Energy: -6.281995+0.000187j, Rel_err(%): 1.7824
[2025-06-05 13:11:38] Iter: 1356/20000, R3[655/800], Temp: 0.0789, Energy: -6.281740-0.000992j, Rel_err(%): 1.7865
[2025-06-05 13:11:41] Iter: 1357/20000, R3[656/800], Temp: 0.0778, Energy: -6.282445+0.000038j, Rel_err(%): 1.7754
[2025-06-05 13:11:45] Iter: 1358/20000, R3[657/800], Temp: 0.0768, Energy: -6.281956-0.001572j, Rel_err(%): 1.7832
[2025-06-05 13:11:48] Iter: 1359/20000, R3[658/800], Temp: 0.0757, Energy: -6.282217-0.000896j, Rel_err(%): 1.7790
[2025-06-05 13:11:51] Iter: 1360/20000, R3[659/800], Temp: 0.0747, Energy: -6.281171+0.000038j, Rel_err(%): 1.7953
[2025-06-05 13:11:54] Iter: 1361/20000, R3[660/800], Temp: 0.0737, Energy: -6.283345+0.000015j, Rel_err(%): 1.7613
[2025-06-05 13:11:57] Iter: 1362/20000, R3[661/800], Temp: 0.0727, Energy: -6.280146-0.000453j, Rel_err(%): 1.8114
[2025-06-05 13:12:00] Iter: 1363/20000, R3[662/800], Temp: 0.0716, Energy: -6.281247-0.000716j, Rel_err(%): 1.7942
[2025-06-05 13:12:04] Iter: 1364/20000, R3[663/800], Temp: 0.0706, Energy: -6.281836+0.000705j, Rel_err(%): 1.7850
[2025-06-05 13:12:07] Iter: 1365/20000, R3[664/800], Temp: 0.0696, Energy: -6.280472-0.000855j, Rel_err(%): 1.8063
[2025-06-05 13:12:10] Iter: 1366/20000, R3[665/800], Temp: 0.0686, Energy: -6.280907+0.000842j, Rel_err(%): 1.7995
[2025-06-05 13:12:13] Iter: 1367/20000, R3[666/800], Temp: 0.0676, Energy: -6.281936-0.000899j, Rel_err(%): 1.7834
[2025-06-05 13:12:16] Iter: 1368/20000, R3[667/800], Temp: 0.0667, Energy: -6.280895+0.001044j, Rel_err(%): 1.7997
[2025-06-05 13:12:19] Iter: 1369/20000, R3[668/800], Temp: 0.0657, Energy: -6.282667-0.000537j, Rel_err(%): 1.7719
[2025-06-05 13:12:23] Iter: 1370/20000, R3[669/800], Temp: 0.0647, Energy: -6.281827+0.001318j, Rel_err(%): 1.7852
[2025-06-05 13:12:26] Iter: 1371/20000, R3[670/800], Temp: 0.0638, Energy: -6.281513+0.000902j, Rel_err(%): 1.7900
[2025-06-05 13:12:29] Iter: 1372/20000, R3[671/800], Temp: 0.0628, Energy: -6.279761+0.000319j, Rel_err(%): 1.8174
[2025-06-05 13:12:32] Iter: 1373/20000, R3[672/800], Temp: 0.0618, Energy: -6.282997+0.000193j, Rel_err(%): 1.7668
[2025-06-05 13:12:35] Iter: 1374/20000, R3[673/800], Temp: 0.0609, Energy: -6.283299-0.000158j, Rel_err(%): 1.7621
[2025-06-05 13:12:38] Iter: 1375/20000, R3[674/800], Temp: 0.0600, Energy: -6.283173+0.000633j, Rel_err(%): 1.7640
[2025-06-05 13:12:41] Iter: 1376/20000, R3[675/800], Temp: 0.0590, Energy: -6.282967-0.000394j, Rel_err(%): 1.7673
[2025-06-05 13:12:45] Iter: 1377/20000, R3[676/800], Temp: 0.0581, Energy: -6.284705-0.000127j, Rel_err(%): 1.7401
[2025-06-05 13:12:48] Iter: 1378/20000, R3[677/800], Temp: 0.0572, Energy: -6.280725-0.000785j, Rel_err(%): 1.8023
[2025-06-05 13:12:51] Iter: 1379/20000, R3[678/800], Temp: 0.0563, Energy: -6.281738-0.000433j, Rel_err(%): 1.7865
[2025-06-05 13:12:54] Iter: 1380/20000, R3[679/800], Temp: 0.0554, Energy: -6.280695-0.001398j, Rel_err(%): 1.8029
[2025-06-05 13:12:57] Iter: 1381/20000, R3[680/800], Temp: 0.0545, Energy: -6.281269-0.000193j, Rel_err(%): 1.7938
[2025-06-05 13:13:00] Iter: 1382/20000, R3[681/800], Temp: 0.0536, Energy: -6.281529-0.000055j, Rel_err(%): 1.7897
[2025-06-05 13:13:04] Iter: 1383/20000, R3[682/800], Temp: 0.0527, Energy: -6.282505+0.000865j, Rel_err(%): 1.7745
[2025-06-05 13:13:07] Iter: 1384/20000, R3[683/800], Temp: 0.0519, Energy: -6.282616+0.000575j, Rel_err(%): 1.7728
[2025-06-05 13:13:10] Iter: 1385/20000, R3[684/800], Temp: 0.0510, Energy: -6.282655+0.000109j, Rel_err(%): 1.7721
[2025-06-05 13:13:13] Iter: 1386/20000, R3[685/800], Temp: 0.0501, Energy: -6.283007+0.000361j, Rel_err(%): 1.7666
[2025-06-05 13:13:16] Iter: 1387/20000, R3[686/800], Temp: 0.0493, Energy: -6.280122+0.000828j, Rel_err(%): 1.8118
[2025-06-05 13:13:19] Iter: 1388/20000, R3[687/800], Temp: 0.0484, Energy: -6.282560+0.000381j, Rel_err(%): 1.7736
[2025-06-05 13:13:23] Iter: 1389/20000, R3[688/800], Temp: 0.0476, Energy: -6.282345-0.000893j, Rel_err(%): 1.7770
[2025-06-05 13:13:26] Iter: 1390/20000, R3[689/800], Temp: 0.0468, Energy: -6.281906-0.001252j, Rel_err(%): 1.7839
[2025-06-05 13:13:29] Iter: 1391/20000, R3[690/800], Temp: 0.0459, Energy: -6.279907+0.000485j, Rel_err(%): 1.8151
[2025-06-05 13:13:32] Iter: 1392/20000, R3[691/800], Temp: 0.0451, Energy: -6.281453-0.000104j, Rel_err(%): 1.7909
[2025-06-05 13:13:35] Iter: 1393/20000, R3[692/800], Temp: 0.0443, Energy: -6.281991+0.001723j, Rel_err(%): 1.7827
[2025-06-05 13:13:38] Iter: 1394/20000, R3[693/800], Temp: 0.0435, Energy: -6.281887-0.000643j, Rel_err(%): 1.7842
[2025-06-05 13:13:41] Iter: 1395/20000, R3[694/800], Temp: 0.0427, Energy: -6.280735-0.000423j, Rel_err(%): 1.8021
[2025-06-05 13:13:45] Iter: 1396/20000, R3[695/800], Temp: 0.0419, Energy: -6.281849+0.000850j, Rel_err(%): 1.7848
[2025-06-05 13:13:48] Iter: 1397/20000, R3[696/800], Temp: 0.0411, Energy: -6.284315-0.000540j, Rel_err(%): 1.7462
[2025-06-05 13:13:51] Iter: 1398/20000, R3[697/800], Temp: 0.0403, Energy: -6.282386-0.000044j, Rel_err(%): 1.7763
[2025-06-05 13:13:54] Iter: 1399/20000, R3[698/800], Temp: 0.0396, Energy: -6.283986-0.001345j, Rel_err(%): 1.7514
[2025-06-05 13:13:57] Iter: 1400/20000, R3[699/800], Temp: 0.0388, Energy: -6.282281+0.000868j, Rel_err(%): 1.7780
[2025-06-05 13:14:00] Iter: 1401/20000, R3[700/800], Temp: 0.0381, Energy: -6.281882+0.001330j, Rel_err(%): 1.7843
[2025-06-05 13:14:04] Iter: 1402/20000, R3[701/800], Temp: 0.0373, Energy: -6.278871-0.001106j, Rel_err(%): 1.8314
[2025-06-05 13:14:07] Iter: 1403/20000, R3[702/800], Temp: 0.0366, Energy: -6.282248-0.001222j, Rel_err(%): 1.7786
[2025-06-05 13:14:10] Iter: 1404/20000, R3[703/800], Temp: 0.0358, Energy: -6.283893+0.001585j, Rel_err(%): 1.7529
[2025-06-05 13:14:13] Iter: 1405/20000, R3[704/800], Temp: 0.0351, Energy: -6.282410-0.000444j, Rel_err(%): 1.7760
[2025-06-05 13:14:16] Iter: 1406/20000, R3[705/800], Temp: 0.0344, Energy: -6.280809+0.001499j, Rel_err(%): 1.8011
[2025-06-05 13:14:19] Iter: 1407/20000, R3[706/800], Temp: 0.0337, Energy: -6.282883+0.000229j, Rel_err(%): 1.7686
[2025-06-05 13:14:23] Iter: 1408/20000, R3[707/800], Temp: 0.0330, Energy: -6.281470+0.000617j, Rel_err(%): 1.7907
[2025-06-05 13:14:26] Iter: 1409/20000, R3[708/800], Temp: 0.0323, Energy: -6.283013-0.001055j, Rel_err(%): 1.7666
[2025-06-05 13:14:29] Iter: 1410/20000, R3[709/800], Temp: 0.0316, Energy: -6.282249-0.001217j, Rel_err(%): 1.7786
[2025-06-05 13:14:32] Iter: 1411/20000, R3[710/800], Temp: 0.0309, Energy: -6.282473-0.000797j, Rel_err(%): 1.7750
[2025-06-05 13:14:35] Iter: 1412/20000, R3[711/800], Temp: 0.0302, Energy: -6.281407-0.000936j, Rel_err(%): 1.7917
[2025-06-05 13:14:38] Iter: 1413/20000, R3[712/800], Temp: 0.0296, Energy: -6.280283-0.001652j, Rel_err(%): 1.8094
[2025-06-05 13:14:41] Iter: 1414/20000, R3[713/800], Temp: 0.0289, Energy: -6.283023-0.000645j, Rel_err(%): 1.7664
[2025-06-05 13:14:45] Iter: 1415/20000, R3[714/800], Temp: 0.0282, Energy: -6.283295+0.000639j, Rel_err(%): 1.7621
[2025-06-05 13:14:48] Iter: 1416/20000, R3[715/800], Temp: 0.0276, Energy: -6.281445+0.000679j, Rel_err(%): 1.7911
[2025-06-05 13:14:51] Iter: 1417/20000, R3[716/800], Temp: 0.0270, Energy: -6.282872-0.001030j, Rel_err(%): 1.7688
[2025-06-05 13:14:54] Iter: 1418/20000, R3[717/800], Temp: 0.0263, Energy: -6.283214+0.001406j, Rel_err(%): 1.7635
[2025-06-05 13:14:57] Iter: 1419/20000, R3[718/800], Temp: 0.0257, Energy: -6.283156-0.001427j, Rel_err(%): 1.7644
[2025-06-05 13:15:00] Iter: 1420/20000, R3[719/800], Temp: 0.0251, Energy: -6.283810-0.001571j, Rel_err(%): 1.7542
[2025-06-05 13:15:04] Iter: 1421/20000, R3[720/800], Temp: 0.0245, Energy: -6.282102-0.000033j, Rel_err(%): 1.7808
[2025-06-05 13:15:07] Iter: 1422/20000, R3[721/800], Temp: 0.0239, Energy: -6.281324-0.000992j, Rel_err(%): 1.7930
[2025-06-05 13:15:10] Iter: 1423/20000, R3[722/800], Temp: 0.0233, Energy: -6.281814-0.000909j, Rel_err(%): 1.7853
[2025-06-05 13:15:13] Iter: 1424/20000, R3[723/800], Temp: 0.0227, Energy: -6.281820+0.000283j, Rel_err(%): 1.7852
[2025-06-05 13:15:16] Iter: 1425/20000, R3[724/800], Temp: 0.0221, Energy: -6.282812+0.001818j, Rel_err(%): 1.7699
[2025-06-05 13:15:19] Iter: 1426/20000, R3[725/800], Temp: 0.0215, Energy: -6.281007-0.000677j, Rel_err(%): 1.7979
[2025-06-05 13:15:23] Iter: 1427/20000, R3[726/800], Temp: 0.0210, Energy: -6.282290+0.001121j, Rel_err(%): 1.7779
[2025-06-05 13:15:26] Iter: 1428/20000, R3[727/800], Temp: 0.0204, Energy: -6.282417+0.000930j, Rel_err(%): 1.7759
[2025-06-05 13:15:29] Iter: 1429/20000, R3[728/800], Temp: 0.0199, Energy: -6.281302+0.000610j, Rel_err(%): 1.7933
[2025-06-05 13:15:32] Iter: 1430/20000, R3[729/800], Temp: 0.0193, Energy: -6.280544-0.000578j, Rel_err(%): 1.8052
[2025-06-05 13:15:35] Iter: 1431/20000, R3[730/800], Temp: 0.0188, Energy: -6.280378+0.000554j, Rel_err(%): 1.8077
[2025-06-05 13:15:38] Iter: 1432/20000, R3[731/800], Temp: 0.0182, Energy: -6.280753-0.000509j, Rel_err(%): 1.8019
[2025-06-05 13:15:41] Iter: 1433/20000, R3[732/800], Temp: 0.0177, Energy: -6.280631+0.000077j, Rel_err(%): 1.8038
[2025-06-05 13:15:45] Iter: 1434/20000, R3[733/800], Temp: 0.0172, Energy: -6.281247+0.001150j, Rel_err(%): 1.7942
[2025-06-05 13:15:48] Iter: 1435/20000, R3[734/800], Temp: 0.0167, Energy: -6.282464-0.001801j, Rel_err(%): 1.7753
[2025-06-05 13:15:51] Iter: 1436/20000, R3[735/800], Temp: 0.0162, Energy: -6.281399+0.000976j, Rel_err(%): 1.7918
[2025-06-05 13:15:54] Iter: 1437/20000, R3[736/800], Temp: 0.0157, Energy: -6.282202+0.001555j, Rel_err(%): 1.7794
[2025-06-05 13:15:57] Iter: 1438/20000, R3[737/800], Temp: 0.0152, Energy: -6.282251-0.000307j, Rel_err(%): 1.7784
[2025-06-05 13:16:00] Iter: 1439/20000, R3[738/800], Temp: 0.0147, Energy: -6.281514-0.000493j, Rel_err(%): 1.7900
[2025-06-05 13:16:04] Iter: 1440/20000, R3[739/800], Temp: 0.0143, Energy: -6.282366-0.000630j, Rel_err(%): 1.7767
[2025-06-05 13:16:07] Iter: 1441/20000, R3[740/800], Temp: 0.0138, Energy: -6.281962+0.000384j, Rel_err(%): 1.7830
[2025-06-05 13:16:10] Iter: 1442/20000, R3[741/800], Temp: 0.0134, Energy: -6.281191-0.000336j, Rel_err(%): 1.7950
[2025-06-05 13:16:13] Iter: 1443/20000, R3[742/800], Temp: 0.0129, Energy: -6.280677-0.000040j, Rel_err(%): 1.8030
[2025-06-05 13:16:16] Iter: 1444/20000, R3[743/800], Temp: 0.0125, Energy: -6.282674-0.000125j, Rel_err(%): 1.7718
[2025-06-05 13:16:19] Iter: 1445/20000, R3[744/800], Temp: 0.0120, Energy: -6.283059-0.000112j, Rel_err(%): 1.7658
[2025-06-05 13:16:23] Iter: 1446/20000, R3[745/800], Temp: 0.0116, Energy: -6.281505-0.000783j, Rel_err(%): 1.7901
[2025-06-05 13:16:26] Iter: 1447/20000, R3[746/800], Temp: 0.0112, Energy: -6.281711+0.000914j, Rel_err(%): 1.7869
[2025-06-05 13:16:29] Iter: 1448/20000, R3[747/800], Temp: 0.0108, Energy: -6.280978-0.000415j, Rel_err(%): 1.7984
[2025-06-05 13:16:32] Iter: 1449/20000, R3[748/800], Temp: 0.0104, Energy: -6.281270+0.000765j, Rel_err(%): 1.7938
[2025-06-05 13:16:35] Iter: 1450/20000, R3[749/800], Temp: 0.0100, Energy: -6.281822-0.000229j, Rel_err(%): 1.7851
[2025-06-05 13:16:38] Iter: 1451/20000, R3[750/800], Temp: 0.0096, Energy: -6.284109-0.000918j, Rel_err(%): 1.7494
[2025-06-05 13:16:41] Iter: 1452/20000, R3[751/800], Temp: 0.0092, Energy: -6.281135+0.001087j, Rel_err(%): 1.7960
[2025-06-05 13:16:45] Iter: 1453/20000, R3[752/800], Temp: 0.0089, Energy: -6.280500+0.001307j, Rel_err(%): 1.8059
[2025-06-05 13:16:48] Iter: 1454/20000, R3[753/800], Temp: 0.0085, Energy: -6.283628+0.000841j, Rel_err(%): 1.7570
[2025-06-05 13:16:51] Iter: 1455/20000, R3[754/800], Temp: 0.0081, Energy: -6.283794+0.000828j, Rel_err(%): 1.7544
[2025-06-05 13:16:54] Iter: 1456/20000, R3[755/800], Temp: 0.0078, Energy: -6.281312+0.001284j, Rel_err(%): 1.7932
[2025-06-05 13:16:57] Iter: 1457/20000, R3[756/800], Temp: 0.0074, Energy: -6.280964-0.001799j, Rel_err(%): 1.7988
[2025-06-05 13:17:00] Iter: 1458/20000, R3[757/800], Temp: 0.0071, Energy: -6.282608+0.001186j, Rel_err(%): 1.7730
[2025-06-05 13:17:04] Iter: 1459/20000, R3[758/800], Temp: 0.0068, Energy: -6.283345-0.001672j, Rel_err(%): 1.7615
[2025-06-05 13:17:07] Iter: 1460/20000, R3[759/800], Temp: 0.0065, Energy: -6.282203+0.000508j, Rel_err(%): 1.7792
[2025-06-05 13:17:10] Iter: 1461/20000, R3[760/800], Temp: 0.0062, Energy: -6.282065+0.000926j, Rel_err(%): 1.7814
[2025-06-05 13:17:13] Iter: 1462/20000, R3[761/800], Temp: 0.0059, Energy: -6.283407+0.001080j, Rel_err(%): 1.7604
[2025-06-05 13:17:16] Iter: 1463/20000, R3[762/800], Temp: 0.0056, Energy: -6.282931-0.000768j, Rel_err(%): 1.7678
[2025-06-05 13:17:19] Iter: 1464/20000, R3[763/800], Temp: 0.0053, Energy: -6.281295+0.000600j, Rel_err(%): 1.7934
[2025-06-05 13:17:23] Iter: 1465/20000, R3[764/800], Temp: 0.0050, Energy: -6.280538+0.001893j, Rel_err(%): 1.8055
[2025-06-05 13:17:26] Iter: 1466/20000, R3[765/800], Temp: 0.0047, Energy: -6.281570-0.000238j, Rel_err(%): 1.7891
[2025-06-05 13:17:29] Iter: 1467/20000, R3[766/800], Temp: 0.0045, Energy: -6.282563-0.002034j, Rel_err(%): 1.7738
[2025-06-05 13:17:32] Iter: 1468/20000, R3[767/800], Temp: 0.0042, Energy: -6.281511-0.000281j, Rel_err(%): 1.7900
[2025-06-05 13:17:35] Iter: 1469/20000, R3[768/800], Temp: 0.0039, Energy: -6.282611+0.000345j, Rel_err(%): 1.7728
[2025-06-05 13:17:38] Iter: 1470/20000, R3[769/800], Temp: 0.0037, Energy: -6.283315+0.000163j, Rel_err(%): 1.7618
[2025-06-05 13:17:42] Iter: 1471/20000, R3[770/800], Temp: 0.0035, Energy: -6.280131-0.001729j, Rel_err(%): 1.8118
[2025-06-05 13:17:45] Iter: 1472/20000, R3[771/800], Temp: 0.0032, Energy: -6.281824+0.001297j, Rel_err(%): 1.7852
[2025-06-05 13:17:48] Iter: 1473/20000, R3[772/800], Temp: 0.0030, Energy: -6.284510+0.000666j, Rel_err(%): 1.7432
[2025-06-05 13:17:51] Iter: 1474/20000, R3[773/800], Temp: 0.0028, Energy: -6.282481+0.000210j, Rel_err(%): 1.7748
[2025-06-05 13:17:54] Iter: 1475/20000, R3[774/800], Temp: 0.0026, Energy: -6.282159+0.000073j, Rel_err(%): 1.7799
[2025-06-05 13:17:57] Iter: 1476/20000, R3[775/800], Temp: 0.0024, Energy: -6.279307+0.000229j, Rel_err(%): 1.8245
[2025-06-05 13:18:00] Iter: 1477/20000, R3[776/800], Temp: 0.0022, Energy: -6.281947-0.000446j, Rel_err(%): 1.7832
[2025-06-05 13:18:04] Iter: 1478/20000, R3[777/800], Temp: 0.0020, Energy: -6.281644+0.001708j, Rel_err(%): 1.7881
[2025-06-05 13:18:07] Iter: 1479/20000, R3[778/800], Temp: 0.0019, Energy: -6.281349+0.000850j, Rel_err(%): 1.7926
[2025-06-05 13:18:10] Iter: 1480/20000, R3[779/800], Temp: 0.0017, Energy: -6.282097-0.001026j, Rel_err(%): 1.7809
[2025-06-05 13:18:13] Iter: 1481/20000, R3[780/800], Temp: 0.0015, Energy: -6.283022-0.000356j, Rel_err(%): 1.7664
[2025-06-05 13:18:16] Iter: 1482/20000, R3[781/800], Temp: 0.0014, Energy: -6.282080+0.001600j, Rel_err(%): 1.7813
[2025-06-05 13:18:19] Iter: 1483/20000, R3[782/800], Temp: 0.0012, Energy: -6.282069+0.001301j, Rel_err(%): 1.7814
[2025-06-05 13:18:23] Iter: 1484/20000, R3[783/800], Temp: 0.0011, Energy: -6.281390-0.000936j, Rel_err(%): 1.7920
[2025-06-05 13:18:26] Iter: 1485/20000, R3[784/800], Temp: 0.0010, Energy: -6.282740+0.000976j, Rel_err(%): 1.7709
[2025-06-05 13:18:29] Iter: 1486/20000, R3[785/800], Temp: 0.0009, Energy: -6.282196+0.001246j, Rel_err(%): 1.7794
[2025-06-05 13:18:32] Iter: 1487/20000, R3[786/800], Temp: 0.0008, Energy: -6.281402-0.000442j, Rel_err(%): 1.7917
[2025-06-05 13:18:35] Iter: 1488/20000, R3[787/800], Temp: 0.0007, Energy: -6.278516+0.001633j, Rel_err(%): 1.8370
[2025-06-05 13:18:38] Iter: 1489/20000, R3[788/800], Temp: 0.0006, Energy: -6.281127-0.000778j, Rel_err(%): 1.7961
[2025-06-05 13:18:42] Iter: 1490/20000, R3[789/800], Temp: 0.0005, Energy: -6.281852+0.000023j, Rel_err(%): 1.7847
[2025-06-05 13:18:45] Iter: 1491/20000, R3[790/800], Temp: 0.0004, Energy: -6.282497-0.001060j, Rel_err(%): 1.7747
[2025-06-05 13:18:48] Iter: 1492/20000, R3[791/800], Temp: 0.0003, Energy: -6.282734-0.000496j, Rel_err(%): 1.7709
[2025-06-05 13:18:51] Iter: 1493/20000, R3[792/800], Temp: 0.0002, Energy: -6.281316-0.001207j, Rel_err(%): 1.7932
[2025-06-05 13:18:54] Iter: 1494/20000, R3[793/800], Temp: 0.0002, Energy: -6.281895+0.002448j, Rel_err(%): 1.7844
[2025-06-05 13:18:57] Iter: 1495/20000, R3[794/800], Temp: 0.0001, Energy: -6.282562+0.001808j, Rel_err(%): 1.7738
[2025-06-05 13:19:00] Iter: 1496/20000, R3[795/800], Temp: 0.0001, Energy: -6.281664-0.000293j, Rel_err(%): 1.7876
[2025-06-05 13:19:04] Iter: 1497/20000, R3[796/800], Temp: 0.0001, Energy: -6.281665+0.000456j, Rel_err(%): 1.7876
[2025-06-05 13:19:07] Iter: 1498/20000, R3[797/800], Temp: 0.0000, Energy: -6.281356-0.001141j, Rel_err(%): 1.7925
[2025-06-05 13:19:10] Iter: 1499/20000, R3[798/800], Temp: 0.0000, Energy: -6.283373-0.002454j, Rel_err(%): 1.7613
[2025-06-05 13:19:13] Iter: 1500/20000, R3[799/800], Temp: 0.0000, Energy: -6.282016-0.000826j, Rel_err(%): 1.7822
[2025-06-05 13:19:13] RESTART #4 at iteration 1501, Temperature reset from 0.0000 to 1.0000, Next period: 1600 iterations
[2025-06-05 13:19:16] Iter: 1501/20000, R4[0/1600], Temp: 1.0000, Energy: -6.283302-0.000064j, Rel_err(%): 1.7620
[2025-06-05 13:19:19] Iter: 1502/20000, R4[1/1600], Temp: 1.0000, Energy: -6.282223+0.000176j, Rel_err(%): 1.7789
[2025-06-05 13:19:23] Iter: 1503/20000, R4[2/1600], Temp: 1.0000, Energy: -6.283248+0.000281j, Rel_err(%): 1.7629
[2025-06-05 13:19:26] Iter: 1504/20000, R4[3/1600], Temp: 1.0000, Energy: -6.282385-0.000707j, Rel_err(%): 1.7764
[2025-06-05 13:19:29] Iter: 1505/20000, R4[4/1600], Temp: 1.0000, Energy: -6.284194+0.000049j, Rel_err(%): 1.7481
[2025-06-05 13:19:32] Iter: 1506/20000, R4[5/1600], Temp: 1.0000, Energy: -6.282752+0.001048j, Rel_err(%): 1.7707
[2025-06-05 13:19:35] Iter: 1507/20000, R4[6/1600], Temp: 1.0000, Energy: -6.282148+0.000785j, Rel_err(%): 1.7801
[2025-06-05 13:19:38] Iter: 1508/20000, R4[7/1600], Temp: 1.0000, Energy: -6.282761+0.001897j, Rel_err(%): 1.7707
[2025-06-05 13:19:41] Iter: 1509/20000, R4[8/1600], Temp: 0.9999, Energy: -6.282021+0.001729j, Rel_err(%): 1.7822
[2025-06-05 13:19:45] Iter: 1510/20000, R4[9/1600], Temp: 0.9999, Energy: -6.279835+0.002804j, Rel_err(%): 1.8167
[2025-06-05 13:19:48] Iter: 1511/20000, R4[10/1600], Temp: 0.9999, Energy: -6.281129-0.000664j, Rel_err(%): 1.7960
[2025-06-05 13:19:51] Iter: 1512/20000, R4[11/1600], Temp: 0.9999, Energy: -6.281670+0.000278j, Rel_err(%): 1.7875
[2025-06-05 13:19:54] Iter: 1513/20000, R4[12/1600], Temp: 0.9999, Energy: -6.283365+0.001354j, Rel_err(%): 1.7611
[2025-06-05 13:19:57] Iter: 1514/20000, R4[13/1600], Temp: 0.9998, Energy: -6.281212+0.001462j, Rel_err(%): 1.7948
[2025-06-05 13:20:00] Iter: 1515/20000, R4[14/1600], Temp: 0.9998, Energy: -6.281154-0.000317j, Rel_err(%): 1.7956
[2025-06-05 13:20:04] Iter: 1516/20000, R4[15/1600], Temp: 0.9998, Energy: -6.282515-0.000881j, Rel_err(%): 1.7744
[2025-06-05 13:20:07] Iter: 1517/20000, R4[16/1600], Temp: 0.9998, Energy: -6.280146-0.000050j, Rel_err(%): 1.8113
[2025-06-05 13:20:10] Iter: 1518/20000, R4[17/1600], Temp: 0.9997, Energy: -6.280040+0.000252j, Rel_err(%): 1.8130
[2025-06-05 13:20:13] Iter: 1519/20000, R4[18/1600], Temp: 0.9997, Energy: -6.281719+0.000399j, Rel_err(%): 1.7868
[2025-06-05 13:20:16] Iter: 1520/20000, R4[19/1600], Temp: 0.9997, Energy: -6.283138+0.000574j, Rel_err(%): 1.7646
[2025-06-05 13:20:19] Iter: 1521/20000, R4[20/1600], Temp: 0.9996, Energy: -6.282448+0.002402j, Rel_err(%): 1.7758
[2025-06-05 13:20:23] Iter: 1522/20000, R4[21/1600], Temp: 0.9996, Energy: -6.282339-0.000044j, Rel_err(%): 1.7771
[2025-06-05 13:20:26] Iter: 1523/20000, R4[22/1600], Temp: 0.9995, Energy: -6.281647+0.000875j, Rel_err(%): 1.7879
[2025-06-05 13:20:29] Iter: 1524/20000, R4[23/1600], Temp: 0.9995, Energy: -6.281691-0.000807j, Rel_err(%): 1.7872
[2025-06-05 13:20:32] Iter: 1525/20000, R4[24/1600], Temp: 0.9994, Energy: -6.282219+0.000333j, Rel_err(%): 1.7789
[2025-06-05 13:20:35] Iter: 1526/20000, R4[25/1600], Temp: 0.9994, Energy: -6.282409+0.001065j, Rel_err(%): 1.7761
[2025-06-05 13:20:38] Iter: 1527/20000, R4[26/1600], Temp: 0.9993, Energy: -6.284520+0.000205j, Rel_err(%): 1.7430
[2025-06-05 13:20:42] Iter: 1528/20000, R4[27/1600], Temp: 0.9993, Energy: -6.282022-0.000388j, Rel_err(%): 1.7820
[2025-06-05 13:20:45] Iter: 1529/20000, R4[28/1600], Temp: 0.9992, Energy: -6.280864-0.000295j, Rel_err(%): 1.8001
[2025-06-05 13:20:48] Iter: 1530/20000, R4[29/1600], Temp: 0.9992, Energy: -6.281652-0.001050j, Rel_err(%): 1.7879
[2025-06-05 13:20:51] Iter: 1531/20000, R4[30/1600], Temp: 0.9991, Energy: -6.280449+0.000524j, Rel_err(%): 1.8066
[2025-06-05 13:20:54] Iter: 1532/20000, R4[31/1600], Temp: 0.9991, Energy: -6.282455-0.001804j, Rel_err(%): 1.7755
[2025-06-05 13:20:57] Iter: 1533/20000, R4[32/1600], Temp: 0.9990, Energy: -6.281828+0.001195j, Rel_err(%): 1.7852
[2025-06-05 13:21:00] Iter: 1534/20000, R4[33/1600], Temp: 0.9990, Energy: -6.280262-0.000784j, Rel_err(%): 1.8096
[2025-06-05 13:21:04] Iter: 1535/20000, R4[34/1600], Temp: 0.9989, Energy: -6.280138+0.000646j, Rel_err(%): 1.8115
[2025-06-05 13:21:07] Iter: 1536/20000, R4[35/1600], Temp: 0.9988, Energy: -6.281716+0.000225j, Rel_err(%): 1.7868
[2025-06-05 13:21:10] Iter: 1537/20000, R4[36/1600], Temp: 0.9988, Energy: -6.282712+0.000691j, Rel_err(%): 1.7713
[2025-06-05 13:21:13] Iter: 1538/20000, R4[37/1600], Temp: 0.9987, Energy: -6.279834-0.000199j, Rel_err(%): 1.8162
[2025-06-05 13:21:16] Iter: 1539/20000, R4[38/1600], Temp: 0.9986, Energy: -6.282127+0.000206j, Rel_err(%): 1.7804
[2025-06-05 13:21:19] Iter: 1540/20000, R4[39/1600], Temp: 0.9985, Energy: -6.281883+0.001894j, Rel_err(%): 1.7844
[2025-06-05 13:21:23] Iter: 1541/20000, R4[40/1600], Temp: 0.9985, Energy: -6.284979-0.000233j, Rel_err(%): 1.7358
[2025-06-05 13:21:26] Iter: 1542/20000, R4[41/1600], Temp: 0.9984, Energy: -6.281809-0.000653j, Rel_err(%): 1.7854
[2025-06-05 13:21:29] Iter: 1543/20000, R4[42/1600], Temp: 0.9983, Energy: -6.281107-0.000907j, Rel_err(%): 1.7964
[2025-06-05 13:21:32] Iter: 1544/20000, R4[43/1600], Temp: 0.9982, Energy: -6.282075+0.001071j, Rel_err(%): 1.7813
[2025-06-05 13:21:35] Iter: 1545/20000, R4[44/1600], Temp: 0.9981, Energy: -6.282363-0.000847j, Rel_err(%): 1.7767
[2025-06-05 13:21:38] Iter: 1546/20000, R4[45/1600], Temp: 0.9980, Energy: -6.279564+0.000745j, Rel_err(%): 1.8205
[2025-06-05 13:21:42] Iter: 1547/20000, R4[46/1600], Temp: 0.9980, Energy: -6.280952+0.000752j, Rel_err(%): 1.7988
[2025-06-05 13:21:45] Iter: 1548/20000, R4[47/1600], Temp: 0.9979, Energy: -6.282737-0.000540j, Rel_err(%): 1.7709
[2025-06-05 13:21:48] Iter: 1549/20000, R4[48/1600], Temp: 0.9978, Energy: -6.283244-0.001010j, Rel_err(%): 1.7630
[2025-06-05 13:21:51] Iter: 1550/20000, R4[49/1600], Temp: 0.9977, Energy: -6.282540+0.000624j, Rel_err(%): 1.7739
[2025-06-05 13:21:54] Iter: 1551/20000, R4[50/1600], Temp: 0.9976, Energy: -6.281416-0.000493j, Rel_err(%): 1.7915
[2025-06-05 13:21:57] Iter: 1552/20000, R4[51/1600], Temp: 0.9975, Energy: -6.282400-0.001070j, Rel_err(%): 1.7762
[2025-06-05 13:22:01] Iter: 1553/20000, R4[52/1600], Temp: 0.9974, Energy: -6.281242+0.001866j, Rel_err(%): 1.7944
[2025-06-05 13:22:04] Iter: 1554/20000, R4[53/1600], Temp: 0.9973, Energy: -6.282919-0.000245j, Rel_err(%): 1.7680
[2025-06-05 13:22:07] Iter: 1555/20000, R4[54/1600], Temp: 0.9972, Energy: -6.281079+0.000535j, Rel_err(%): 1.7968
[2025-06-05 13:22:10] Iter: 1556/20000, R4[55/1600], Temp: 0.9971, Energy: -6.282940-0.001536j, Rel_err(%): 1.7678
[2025-06-05 13:22:13] Iter: 1557/20000, R4[56/1600], Temp: 0.9970, Energy: -6.283672-0.001670j, Rel_err(%): 1.7564
[2025-06-05 13:22:16] Iter: 1558/20000, R4[57/1600], Temp: 0.9969, Energy: -6.280626+0.001435j, Rel_err(%): 1.8040
[2025-06-05 13:22:19] Iter: 1559/20000, R4[58/1600], Temp: 0.9968, Energy: -6.284146+0.001872j, Rel_err(%): 1.7491
[2025-06-05 13:22:23] Iter: 1560/20000, R4[59/1600], Temp: 0.9966, Energy: -6.282011-0.001384j, Rel_err(%): 1.7823
[2025-06-05 13:22:26] Iter: 1561/20000, R4[60/1600], Temp: 0.9965, Energy: -6.282675+0.000123j, Rel_err(%): 1.7718
[2025-06-05 13:22:29] Iter: 1562/20000, R4[61/1600], Temp: 0.9964, Energy: -6.283766+0.000473j, Rel_err(%): 1.7548
[2025-06-05 13:22:32] Iter: 1563/20000, R4[62/1600], Temp: 0.9963, Energy: -6.282906+0.002060j, Rel_err(%): 1.7685
[2025-06-05 13:22:35] Iter: 1564/20000, R4[63/1600], Temp: 0.9962, Energy: -6.282176+0.000027j, Rel_err(%): 1.7796
[2025-06-05 13:22:38] Iter: 1565/20000, R4[64/1600], Temp: 0.9961, Energy: -6.281695-0.000062j, Rel_err(%): 1.7871
[2025-06-05 13:22:42] Iter: 1566/20000, R4[65/1600], Temp: 0.9959, Energy: -6.283039-0.000630j, Rel_err(%): 1.7661
[2025-06-05 13:22:45] Iter: 1567/20000, R4[66/1600], Temp: 0.9958, Energy: -6.283023+0.000326j, Rel_err(%): 1.7664
[2025-06-05 13:22:48] Iter: 1568/20000, R4[67/1600], Temp: 0.9957, Energy: -6.283130-0.000415j, Rel_err(%): 1.7647
[2025-06-05 13:22:51] Iter: 1569/20000, R4[68/1600], Temp: 0.9955, Energy: -6.284769+0.000920j, Rel_err(%): 1.7391
[2025-06-05 13:22:54] Iter: 1570/20000, R4[69/1600], Temp: 0.9954, Energy: -6.283561-0.000697j, Rel_err(%): 1.7580
[2025-06-05 13:22:57] Iter: 1571/20000, R4[70/1600], Temp: 0.9953, Energy: -6.281108-0.001242j, Rel_err(%): 1.7964
[2025-06-05 13:23:00] Iter: 1572/20000, R4[71/1600], Temp: 0.9951, Energy: -6.283398+0.000820j, Rel_err(%): 1.7606
[2025-06-05 13:23:04] Iter: 1573/20000, R4[72/1600], Temp: 0.9950, Energy: -6.283700+0.001058j, Rel_err(%): 1.7559
[2025-06-05 13:23:07] Iter: 1574/20000, R4[73/1600], Temp: 0.9949, Energy: -6.281080+0.000148j, Rel_err(%): 1.7967
[2025-06-05 13:23:10] Iter: 1575/20000, R4[74/1600], Temp: 0.9947, Energy: -6.281946-0.000806j, Rel_err(%): 1.7833
[2025-06-05 13:23:13] Iter: 1576/20000, R4[75/1600], Temp: 0.9946, Energy: -6.284333-0.000902j, Rel_err(%): 1.7459
[2025-06-05 13:23:16] Iter: 1577/20000, R4[76/1600], Temp: 0.9944, Energy: -6.281969-0.000545j, Rel_err(%): 1.7829
[2025-06-05 13:23:19] Iter: 1578/20000, R4[77/1600], Temp: 0.9943, Energy: -6.282561-0.001689j, Rel_err(%): 1.7738
[2025-06-05 13:23:23] Iter: 1579/20000, R4[78/1600], Temp: 0.9941, Energy: -6.283308+0.000813j, Rel_err(%): 1.7620
[2025-06-05 13:23:26] Iter: 1580/20000, R4[79/1600], Temp: 0.9940, Energy: -6.284597+0.000259j, Rel_err(%): 1.7418
[2025-06-05 13:23:29] Iter: 1581/20000, R4[80/1600], Temp: 0.9938, Energy: -6.282223+0.000412j, Rel_err(%): 1.7789
[2025-06-05 13:23:32] Iter: 1582/20000, R4[81/1600], Temp: 0.9937, Energy: -6.280979-0.001237j, Rel_err(%): 1.7984
[2025-06-05 13:23:35] Iter: 1583/20000, R4[82/1600], Temp: 0.9935, Energy: -6.280691+0.000636j, Rel_err(%): 1.8028
[2025-06-05 13:23:38] Iter: 1584/20000, R4[83/1600], Temp: 0.9934, Energy: -6.283416-0.000306j, Rel_err(%): 1.7602
[2025-06-05 13:23:42] Iter: 1585/20000, R4[84/1600], Temp: 0.9932, Energy: -6.283461+0.000165j, Rel_err(%): 1.7595
[2025-06-05 13:23:45] Iter: 1586/20000, R4[85/1600], Temp: 0.9931, Energy: -6.282436+0.000145j, Rel_err(%): 1.7755
[2025-06-05 13:23:48] Iter: 1587/20000, R4[86/1600], Temp: 0.9929, Energy: -6.283578+0.000066j, Rel_err(%): 1.7577
[2025-06-05 13:23:51] Iter: 1588/20000, R4[87/1600], Temp: 0.9927, Energy: -6.281689-0.000326j, Rel_err(%): 1.7872
[2025-06-05 13:23:54] Iter: 1589/20000, R4[88/1600], Temp: 0.9926, Energy: -6.281924+0.000606j, Rel_err(%): 1.7836
[2025-06-05 13:23:57] Iter: 1590/20000, R4[89/1600], Temp: 0.9924, Energy: -6.282572+0.001368j, Rel_err(%): 1.7735
[2025-06-05 13:24:00] Iter: 1591/20000, R4[90/1600], Temp: 0.9922, Energy: -6.283168+0.001155j, Rel_err(%): 1.7642
[2025-06-05 13:24:04] Iter: 1592/20000, R4[91/1600], Temp: 0.9920, Energy: -6.284591+0.001260j, Rel_err(%): 1.7420
[2025-06-05 13:24:07] Iter: 1593/20000, R4[92/1600], Temp: 0.9919, Energy: -6.281149+0.001982j, Rel_err(%): 1.7959
[2025-06-05 13:24:10] Iter: 1594/20000, R4[93/1600], Temp: 0.9917, Energy: -6.283146+0.001390j, Rel_err(%): 1.7646
[2025-06-05 13:24:13] Iter: 1595/20000, R4[94/1600], Temp: 0.9915, Energy: -6.282508+0.000253j, Rel_err(%): 1.7744
[2025-06-05 13:24:16] Iter: 1596/20000, R4[95/1600], Temp: 0.9913, Energy: -6.281790-0.000974j, Rel_err(%): 1.7857
[2025-06-05 13:24:19] Iter: 1597/20000, R4[96/1600], Temp: 0.9911, Energy: -6.281953-0.000682j, Rel_err(%): 1.7831
[2025-06-05 13:24:23] Iter: 1598/20000, R4[97/1600], Temp: 0.9910, Energy: -6.282005-0.000638j, Rel_err(%): 1.7823
[2025-06-05 13:24:26] Iter: 1599/20000, R4[98/1600], Temp: 0.9908, Energy: -6.280579+0.000193j, Rel_err(%): 1.8046
[2025-06-05 13:24:29] Iter: 1600/20000, R4[99/1600], Temp: 0.9906, Energy: -6.284474-0.000499j, Rel_err(%): 1.7437
[2025-06-05 13:24:32] Iter: 1601/20000, R4[100/1600], Temp: 0.9904, Energy: -6.279896-0.000628j, Rel_err(%): 1.8153
[2025-06-05 13:24:35] Iter: 1602/20000, R4[101/1600], Temp: 0.9902, Energy: -6.282202-0.000459j, Rel_err(%): 1.7792
[2025-06-05 13:24:38] Iter: 1603/20000, R4[102/1600], Temp: 0.9900, Energy: -6.281246+0.000329j, Rel_err(%): 1.7942
[2025-06-05 13:24:42] Iter: 1604/20000, R4[103/1600], Temp: 0.9898, Energy: -6.280938-0.000202j, Rel_err(%): 1.7990
[2025-06-05 13:24:45] Iter: 1605/20000, R4[104/1600], Temp: 0.9896, Energy: -6.281634+0.001232j, Rel_err(%): 1.7882
[2025-06-05 13:24:48] Iter: 1606/20000, R4[105/1600], Temp: 0.9894, Energy: -6.282653+0.001333j, Rel_err(%): 1.7723
[2025-06-05 13:24:51] Iter: 1607/20000, R4[106/1600], Temp: 0.9892, Energy: -6.282827+0.001876j, Rel_err(%): 1.7697
[2025-06-05 13:24:54] Iter: 1608/20000, R4[107/1600], Temp: 0.9890, Energy: -6.283392-0.003158j, Rel_err(%): 1.7613
[2025-06-05 13:24:57] Iter: 1609/20000, R4[108/1600], Temp: 0.9888, Energy: -6.282413+0.000971j, Rel_err(%): 1.7760
[2025-06-05 13:25:00] Iter: 1610/20000, R4[109/1600], Temp: 0.9886, Energy: -6.282826+0.000962j, Rel_err(%): 1.7695
[2025-06-05 13:25:04] Iter: 1611/20000, R4[110/1600], Temp: 0.9884, Energy: -6.285093+0.001487j, Rel_err(%): 1.7342
[2025-06-05 13:25:07] Iter: 1612/20000, R4[111/1600], Temp: 0.9882, Energy: -6.281452+0.000492j, Rel_err(%): 1.7910
[2025-06-05 13:25:10] Iter: 1613/20000, R4[112/1600], Temp: 0.9880, Energy: -6.282098-0.000298j, Rel_err(%): 1.7808
[2025-06-05 13:25:13] Iter: 1614/20000, R4[113/1600], Temp: 0.9877, Energy: -6.283986-0.000706j, Rel_err(%): 1.7513
[2025-06-05 13:25:16] Iter: 1615/20000, R4[114/1600], Temp: 0.9875, Energy: -6.281568+0.001236j, Rel_err(%): 1.7892
[2025-06-05 13:25:19] Iter: 1616/20000, R4[115/1600], Temp: 0.9873, Energy: -6.281880-0.001533j, Rel_err(%): 1.7844
[2025-06-05 13:25:23] Iter: 1617/20000, R4[116/1600], Temp: 0.9871, Energy: -6.284167+0.000293j, Rel_err(%): 1.7485
[2025-06-05 13:25:26] Iter: 1618/20000, R4[117/1600], Temp: 0.9869, Energy: -6.284327-0.001165j, Rel_err(%): 1.7461
[2025-06-05 13:25:29] Iter: 1619/20000, R4[118/1600], Temp: 0.9866, Energy: -6.283700+0.002000j, Rel_err(%): 1.7561
[2025-06-05 13:25:32] Iter: 1620/20000, R4[119/1600], Temp: 0.9864, Energy: -6.282935-0.000121j, Rel_err(%): 1.7677
[2025-06-05 13:25:35] Iter: 1621/20000, R4[120/1600], Temp: 0.9862, Energy: -6.284010+0.000915j, Rel_err(%): 1.7510
[2025-06-05 13:25:38] Iter: 1622/20000, R4[121/1600], Temp: 0.9860, Energy: -6.283378+0.001952j, Rel_err(%): 1.7611
[2025-06-05 13:25:42] Iter: 1623/20000, R4[122/1600], Temp: 0.9857, Energy: -6.283621-0.000982j, Rel_err(%): 1.7571
[2025-06-05 13:25:45] Iter: 1624/20000, R4[123/1600], Temp: 0.9855, Energy: -6.285257-0.001304j, Rel_err(%): 1.7316
[2025-06-05 13:25:48] Iter: 1625/20000, R4[124/1600], Temp: 0.9853, Energy: -6.281128-0.000271j, Rel_err(%): 1.7960
[2025-06-05 13:25:51] Iter: 1626/20000, R4[125/1600], Temp: 0.9850, Energy: -6.280687+0.000202j, Rel_err(%): 1.8029
[2025-06-05 13:25:54] Iter: 1627/20000, R4[126/1600], Temp: 0.9848, Energy: -6.280842+0.000971j, Rel_err(%): 1.8005
[2025-06-05 13:25:57] Iter: 1628/20000, R4[127/1600], Temp: 0.9845, Energy: -6.284060-0.000321j, Rel_err(%): 1.7502
[2025-06-05 13:26:00] Iter: 1629/20000, R4[128/1600], Temp: 0.9843, Energy: -6.281775+0.001842j, Rel_err(%): 1.7861
[2025-06-05 13:26:04] Iter: 1630/20000, R4[129/1600], Temp: 0.9840, Energy: -6.282826+0.000500j, Rel_err(%): 1.7695
[2025-06-05 13:26:07] Iter: 1631/20000, R4[130/1600], Temp: 0.9838, Energy: -6.283403-0.000155j, Rel_err(%): 1.7604
[2025-06-05 13:26:10] Iter: 1632/20000, R4[131/1600], Temp: 0.9836, Energy: -6.283118+0.000669j, Rel_err(%): 1.7649
[2025-06-05 13:26:13] Iter: 1633/20000, R4[132/1600], Temp: 0.9833, Energy: -6.282660-0.000083j, Rel_err(%): 1.7720
[2025-06-05 13:26:16] Iter: 1634/20000, R4[133/1600], Temp: 0.9830, Energy: -6.282745-0.000310j, Rel_err(%): 1.7707
[2025-06-05 13:26:19] Iter: 1635/20000, R4[134/1600], Temp: 0.9828, Energy: -6.281806-0.000014j, Rel_err(%): 1.7854
[2025-06-05 13:26:23] Iter: 1636/20000, R4[135/1600], Temp: 0.9825, Energy: -6.281798+0.001056j, Rel_err(%): 1.7856
[2025-06-05 13:26:26] Iter: 1637/20000, R4[136/1600], Temp: 0.9823, Energy: -6.282179+0.000876j, Rel_err(%): 1.7796
[2025-06-05 13:26:29] Iter: 1638/20000, R4[137/1600], Temp: 0.9820, Energy: -6.283821+0.000789j, Rel_err(%): 1.7539
[2025-06-05 13:26:32] Iter: 1639/20000, R4[138/1600], Temp: 0.9818, Energy: -6.281497+0.000428j, Rel_err(%): 1.7902
[2025-06-05 13:26:35] Iter: 1640/20000, R4[139/1600], Temp: 0.9815, Energy: -6.282162+0.000459j, Rel_err(%): 1.7798
[2025-06-05 13:26:38] Iter: 1641/20000, R4[140/1600], Temp: 0.9812, Energy: -6.283196-0.000607j, Rel_err(%): 1.7637
[2025-06-05 13:26:42] Iter: 1642/20000, R4[141/1600], Temp: 0.9810, Energy: -6.283060+0.001076j, Rel_err(%): 1.7659
[2025-06-05 13:26:45] Iter: 1643/20000, R4[142/1600], Temp: 0.9807, Energy: -6.282845-0.001789j, Rel_err(%): 1.7694
[2025-06-05 13:26:48] Iter: 1644/20000, R4[143/1600], Temp: 0.9804, Energy: -6.283081-0.000209j, Rel_err(%): 1.7655
[2025-06-05 13:26:51] Iter: 1645/20000, R4[144/1600], Temp: 0.9801, Energy: -6.282805+0.000027j, Rel_err(%): 1.7698
[2025-06-05 13:26:54] Iter: 1646/20000, R4[145/1600], Temp: 0.9799, Energy: -6.283931+0.000147j, Rel_err(%): 1.7522
[2025-06-05 13:26:57] Iter: 1647/20000, R4[146/1600], Temp: 0.9796, Energy: -6.285416-0.000121j, Rel_err(%): 1.7290
[2025-06-05 13:27:00] Iter: 1648/20000, R4[147/1600], Temp: 0.9793, Energy: -6.284156-0.000232j, Rel_err(%): 1.7487
[2025-06-05 13:27:04] Iter: 1649/20000, R4[148/1600], Temp: 0.9790, Energy: -6.283579+0.000774j, Rel_err(%): 1.7577
[2025-06-05 13:27:07] Iter: 1650/20000, R4[149/1600], Temp: 0.9788, Energy: -6.284389-0.000408j, Rel_err(%): 1.7450
[2025-06-05 13:27:10] Iter: 1651/20000, R4[150/1600], Temp: 0.9785, Energy: -6.282595-0.000183j, Rel_err(%): 1.7731
[2025-06-05 13:27:13] Iter: 1652/20000, R4[151/1600], Temp: 0.9782, Energy: -6.282352-0.000435j, Rel_err(%): 1.7769
[2025-06-05 13:27:16] Iter: 1653/20000, R4[152/1600], Temp: 0.9779, Energy: -6.282266+0.002102j, Rel_err(%): 1.7785
[2025-06-05 13:27:19] Iter: 1654/20000, R4[153/1600], Temp: 0.9776, Energy: -6.284546-0.000925j, Rel_err(%): 1.7426
[2025-06-05 13:27:23] Iter: 1655/20000, R4[154/1600], Temp: 0.9773, Energy: -6.282361+0.000487j, Rel_err(%): 1.7767
[2025-06-05 13:27:26] Iter: 1656/20000, R4[155/1600], Temp: 0.9770, Energy: -6.282933-0.001636j, Rel_err(%): 1.7680
[2025-06-05 13:27:29] Iter: 1657/20000, R4[156/1600], Temp: 0.9767, Energy: -6.282745+0.000892j, Rel_err(%): 1.7708
[2025-06-05 13:27:32] Iter: 1658/20000, R4[157/1600], Temp: 0.9764, Energy: -6.284666+0.000189j, Rel_err(%): 1.7407
[2025-06-05 13:27:35] Iter: 1659/20000, R4[158/1600], Temp: 0.9761, Energy: -6.284496-0.001417j, Rel_err(%): 1.7435
[2025-06-05 13:27:38] Iter: 1660/20000, R4[159/1600], Temp: 0.9758, Energy: -6.283366-0.000060j, Rel_err(%): 1.7610
[2025-06-05 13:27:42] Iter: 1661/20000, R4[160/1600], Temp: 0.9755, Energy: -6.281997-0.000931j, Rel_err(%): 1.7825
[2025-06-05 13:27:45] Iter: 1662/20000, R4[161/1600], Temp: 0.9752, Energy: -6.282630-0.001006j, Rel_err(%): 1.7726
[2025-06-05 13:27:48] Iter: 1663/20000, R4[162/1600], Temp: 0.9749, Energy: -6.281832-0.001626j, Rel_err(%): 1.7852
[2025-06-05 13:27:51] Iter: 1664/20000, R4[163/1600], Temp: 0.9746, Energy: -6.280557-0.000064j, Rel_err(%): 1.8049
[2025-06-05 13:27:54] Iter: 1665/20000, R4[164/1600], Temp: 0.9743, Energy: -6.283243+0.001190j, Rel_err(%): 1.7630
[2025-06-05 13:27:57] Iter: 1666/20000, R4[165/1600], Temp: 0.9740, Energy: -6.286014+0.000995j, Rel_err(%): 1.7197
[2025-06-05 13:28:00] Iter: 1667/20000, R4[166/1600], Temp: 0.9737, Energy: -6.281117-0.000222j, Rel_err(%): 1.7962
[2025-06-05 13:28:04] Iter: 1668/20000, R4[167/1600], Temp: 0.9734, Energy: -6.281284-0.000726j, Rel_err(%): 1.7936
[2025-06-05 13:28:07] Iter: 1669/20000, R4[168/1600], Temp: 0.9730, Energy: -6.281657-0.001559j, Rel_err(%): 1.7879
[2025-06-05 13:28:10] Iter: 1670/20000, R4[169/1600], Temp: 0.9727, Energy: -6.280520+0.001591j, Rel_err(%): 1.8057
[2025-06-05 13:28:13] Iter: 1671/20000, R4[170/1600], Temp: 0.9724, Energy: -6.281280-0.000842j, Rel_err(%): 1.7937
[2025-06-05 13:28:16] Iter: 1672/20000, R4[171/1600], Temp: 0.9721, Energy: -6.284098+0.000134j, Rel_err(%): 1.7496
[2025-06-05 13:28:19] Iter: 1673/20000, R4[172/1600], Temp: 0.9718, Energy: -6.281777-0.001267j, Rel_err(%): 1.7860
[2025-06-05 13:28:23] Iter: 1674/20000, R4[173/1600], Temp: 0.9714, Energy: -6.283259-0.000667j, Rel_err(%): 1.7627
[2025-06-05 13:28:26] Iter: 1675/20000, R4[174/1600], Temp: 0.9711, Energy: -6.281992-0.002479j, Rel_err(%): 1.7829
[2025-06-05 13:28:29] Iter: 1676/20000, R4[175/1600], Temp: 0.9708, Energy: -6.282274-0.001098j, Rel_err(%): 1.7782
[2025-06-05 13:28:32] Iter: 1677/20000, R4[176/1600], Temp: 0.9704, Energy: -6.285599-0.000378j, Rel_err(%): 1.7261
[2025-06-05 13:28:35] Iter: 1678/20000, R4[177/1600], Temp: 0.9701, Energy: -6.283756-0.000880j, Rel_err(%): 1.7550
[2025-06-05 13:28:38] Iter: 1679/20000, R4[178/1600], Temp: 0.9698, Energy: -6.281089-0.001150j, Rel_err(%): 1.7967
[2025-06-05 13:28:41] Iter: 1680/20000, R4[179/1600], Temp: 0.9694, Energy: -6.283428-0.000985j, Rel_err(%): 1.7601
[2025-06-05 13:28:45] Iter: 1681/20000, R4[180/1600], Temp: 0.9691, Energy: -6.283147+0.001060j, Rel_err(%): 1.7645
[2025-06-05 13:28:48] Iter: 1682/20000, R4[181/1600], Temp: 0.9688, Energy: -6.280885-0.000558j, Rel_err(%): 1.7998
[2025-06-05 13:28:51] Iter: 1683/20000, R4[182/1600], Temp: 0.9684, Energy: -6.283448-0.000773j, Rel_err(%): 1.7598
[2025-06-05 13:28:54] Iter: 1684/20000, R4[183/1600], Temp: 0.9681, Energy: -6.282702-0.001078j, Rel_err(%): 1.7715
[2025-06-05 13:28:57] Iter: 1685/20000, R4[184/1600], Temp: 0.9677, Energy: -6.284483+0.000937j, Rel_err(%): 1.7436
[2025-06-05 13:29:00] Iter: 1686/20000, R4[185/1600], Temp: 0.9674, Energy: -6.282266+0.000216j, Rel_err(%): 1.7782
[2025-06-05 13:29:04] Iter: 1687/20000, R4[186/1600], Temp: 0.9670, Energy: -6.283677-0.000381j, Rel_err(%): 1.7562
[2025-06-05 13:29:07] Iter: 1688/20000, R4[187/1600], Temp: 0.9667, Energy: -6.284470+0.001368j, Rel_err(%): 1.7439
[2025-06-05 13:29:10] Iter: 1689/20000, R4[188/1600], Temp: 0.9663, Energy: -6.283577+0.001031j, Rel_err(%): 1.7578
[2025-06-05 13:29:13] Iter: 1690/20000, R4[189/1600], Temp: 0.9660, Energy: -6.284409+0.000500j, Rel_err(%): 1.7447
[2025-06-05 13:29:16] Iter: 1691/20000, R4[190/1600], Temp: 0.9656, Energy: -6.282541-0.000628j, Rel_err(%): 1.7739
[2025-06-05 13:29:19] Iter: 1692/20000, R4[191/1600], Temp: 0.9652, Energy: -6.283067-0.000830j, Rel_err(%): 1.7657
[2025-06-05 13:29:23] Iter: 1693/20000, R4[192/1600], Temp: 0.9649, Energy: -6.283048+0.000713j, Rel_err(%): 1.7660
[2025-06-05 13:29:26] Iter: 1694/20000, R4[193/1600], Temp: 0.9645, Energy: -6.283164+0.000213j, Rel_err(%): 1.7642
[2025-06-05 13:29:29] Iter: 1695/20000, R4[194/1600], Temp: 0.9642, Energy: -6.283376+0.000327j, Rel_err(%): 1.7609
[2025-06-05 13:29:32] Iter: 1696/20000, R4[195/1600], Temp: 0.9638, Energy: -6.281736+0.000067j, Rel_err(%): 1.7865
[2025-06-05 13:29:35] Iter: 1697/20000, R4[196/1600], Temp: 0.9634, Energy: -6.281042-0.000084j, Rel_err(%): 1.7973
[2025-06-05 13:29:38] Iter: 1698/20000, R4[197/1600], Temp: 0.9631, Energy: -6.281958+0.000163j, Rel_err(%): 1.7830
[2025-06-05 13:29:41] Iter: 1699/20000, R4[198/1600], Temp: 0.9627, Energy: -6.283808-0.000494j, Rel_err(%): 1.7541
[2025-06-05 13:29:45] Iter: 1700/20000, R4[199/1600], Temp: 0.9623, Energy: -6.284118+0.001348j, Rel_err(%): 1.7494
[2025-06-05 13:29:48] Iter: 1701/20000, R4[200/1600], Temp: 0.9619, Energy: -6.281977+0.001121j, Rel_err(%): 1.7828
[2025-06-05 13:29:51] Iter: 1702/20000, R4[201/1600], Temp: 0.9616, Energy: -6.285448-0.000542j, Rel_err(%): 1.7285
[2025-06-05 13:29:54] Iter: 1703/20000, R4[202/1600], Temp: 0.9612, Energy: -6.283907-0.001597j, Rel_err(%): 1.7527
[2025-06-05 13:29:57] Iter: 1704/20000, R4[203/1600], Temp: 0.9608, Energy: -6.283327+0.000185j, Rel_err(%): 1.7616
[2025-06-05 13:30:00] Iter: 1705/20000, R4[204/1600], Temp: 0.9604, Energy: -6.282741-0.001290j, Rel_err(%): 1.7709
[2025-06-05 13:30:04] Iter: 1706/20000, R4[205/1600], Temp: 0.9600, Energy: -6.284467+0.001065j, Rel_err(%): 1.7439
[2025-06-05 13:30:07] Iter: 1707/20000, R4[206/1600], Temp: 0.9597, Energy: -6.283249-0.000686j, Rel_err(%): 1.7629
[2025-06-05 13:30:10] Iter: 1708/20000, R4[207/1600], Temp: 0.9593, Energy: -6.281218-0.001910j, Rel_err(%): 1.7948
[2025-06-05 13:30:13] Iter: 1709/20000, R4[208/1600], Temp: 0.9589, Energy: -6.284266-0.000723j, Rel_err(%): 1.7470
[2025-06-05 13:30:16] Iter: 1710/20000, R4[209/1600], Temp: 0.9585, Energy: -6.284457-0.000398j, Rel_err(%): 1.7440
[2025-06-05 13:30:19] Iter: 1711/20000, R4[210/1600], Temp: 0.9581, Energy: -6.283889+0.000213j, Rel_err(%): 1.7528
[2025-06-05 13:30:23] Iter: 1712/20000, R4[211/1600], Temp: 0.9577, Energy: -6.282539+0.000247j, Rel_err(%): 1.7739
[2025-06-05 13:30:26] Iter: 1713/20000, R4[212/1600], Temp: 0.9573, Energy: -6.283882-0.000055j, Rel_err(%): 1.7529
[2025-06-05 13:30:29] Iter: 1714/20000, R4[213/1600], Temp: 0.9569, Energy: -6.284691-0.000274j, Rel_err(%): 1.7403
[2025-06-05 13:30:32] Iter: 1715/20000, R4[214/1600], Temp: 0.9565, Energy: -6.284066+0.000354j, Rel_err(%): 1.7501
[2025-06-05 13:30:35] Iter: 1716/20000, R4[215/1600], Temp: 0.9561, Energy: -6.281325-0.001134j, Rel_err(%): 1.7930
[2025-06-05 13:30:38] Iter: 1717/20000, R4[216/1600], Temp: 0.9557, Energy: -6.282572-0.000441j, Rel_err(%): 1.7734
[2025-06-05 13:30:42] Iter: 1718/20000, R4[217/1600], Temp: 0.9553, Energy: -6.283173-0.001332j, Rel_err(%): 1.7642
[2025-06-05 13:30:45] Iter: 1719/20000, R4[218/1600], Temp: 0.9549, Energy: -6.282601+0.001330j, Rel_err(%): 1.7731
[2025-06-05 13:30:48] Iter: 1720/20000, R4[219/1600], Temp: 0.9545, Energy: -6.283027-0.000033j, Rel_err(%): 1.7663
[2025-06-05 13:30:51] Iter: 1721/20000, R4[220/1600], Temp: 0.9541, Energy: -6.282457+0.000268j, Rel_err(%): 1.7752
[2025-06-05 13:30:54] Iter: 1722/20000, R4[221/1600], Temp: 0.9537, Energy: -6.283886-0.000542j, Rel_err(%): 1.7529
[2025-06-05 13:30:57] Iter: 1723/20000, R4[222/1600], Temp: 0.9532, Energy: -6.282889-0.000371j, Rel_err(%): 1.7685
[2025-06-05 13:31:00] Iter: 1724/20000, R4[223/1600], Temp: 0.9528, Energy: -6.282688-0.001515j, Rel_err(%): 1.7718
[2025-06-05 13:31:04] Iter: 1725/20000, R4[224/1600], Temp: 0.9524, Energy: -6.283308-0.000256j, Rel_err(%): 1.7619
[2025-06-05 13:31:07] Iter: 1726/20000, R4[225/1600], Temp: 0.9520, Energy: -6.283499+0.000442j, Rel_err(%): 1.7589
[2025-06-05 13:31:10] Iter: 1727/20000, R4[226/1600], Temp: 0.9516, Energy: -6.282326-0.001565j, Rel_err(%): 1.7774
[2025-06-05 13:31:13] Iter: 1728/20000, R4[227/1600], Temp: 0.9512, Energy: -6.284590-0.001572j, Rel_err(%): 1.7420
[2025-06-05 13:31:16] Iter: 1729/20000, R4[228/1600], Temp: 0.9507, Energy: -6.284230-0.000006j, Rel_err(%): 1.7475
[2025-06-05 13:31:19] Iter: 1730/20000, R4[229/1600], Temp: 0.9503, Energy: -6.283167-0.000359j, Rel_err(%): 1.7641
[2025-06-05 13:31:23] Iter: 1731/20000, R4[230/1600], Temp: 0.9499, Energy: -6.284831+0.000397j, Rel_err(%): 1.7381
[2025-06-05 13:31:26] Iter: 1732/20000, R4[231/1600], Temp: 0.9494, Energy: -6.283531-0.000903j, Rel_err(%): 1.7585
[2025-06-05 13:31:29] Iter: 1733/20000, R4[232/1600], Temp: 0.9490, Energy: -6.281523+0.000527j, Rel_err(%): 1.7898
[2025-06-05 13:31:32] Iter: 1734/20000, R4[233/1600], Temp: 0.9486, Energy: -6.282696-0.000552j, Rel_err(%): 1.7715
[2025-06-05 13:31:35] Iter: 1735/20000, R4[234/1600], Temp: 0.9481, Energy: -6.283692-0.001012j, Rel_err(%): 1.7560
[2025-06-05 13:31:38] Iter: 1736/20000, R4[235/1600], Temp: 0.9477, Energy: -6.283398+0.000199j, Rel_err(%): 1.7605
[2025-06-05 13:31:42] Iter: 1737/20000, R4[236/1600], Temp: 0.9473, Energy: -6.281769+0.000068j, Rel_err(%): 1.7860
[2025-06-05 13:31:45] Iter: 1738/20000, R4[237/1600], Temp: 0.9468, Energy: -6.281858+0.000024j, Rel_err(%): 1.7846
[2025-06-05 13:31:48] Iter: 1739/20000, R4[238/1600], Temp: 0.9464, Energy: -6.282389+0.001212j, Rel_err(%): 1.7764
[2025-06-05 13:31:51] Iter: 1740/20000, R4[239/1600], Temp: 0.9459, Energy: -6.283768+0.000001j, Rel_err(%): 1.7547
[2025-06-05 13:31:54] Iter: 1741/20000, R4[240/1600], Temp: 0.9455, Energy: -6.283384+0.000436j, Rel_err(%): 1.7607
[2025-06-05 13:31:57] Iter: 1742/20000, R4[241/1600], Temp: 0.9451, Energy: -6.283830-0.000877j, Rel_err(%): 1.7538
[2025-06-05 13:32:01] Iter: 1743/20000, R4[242/1600], Temp: 0.9446, Energy: -6.282990-0.000516j, Rel_err(%): 1.7669
[2025-06-05 13:32:04] Iter: 1744/20000, R4[243/1600], Temp: 0.9442, Energy: -6.283385+0.000423j, Rel_err(%): 1.7607
[2025-06-05 13:32:07] Iter: 1745/20000, R4[244/1600], Temp: 0.9437, Energy: -6.283607-0.000943j, Rel_err(%): 1.7573
[2025-06-05 13:32:10] Iter: 1746/20000, R4[245/1600], Temp: 0.9433, Energy: -6.283021-0.000277j, Rel_err(%): 1.7664
[2025-06-05 13:32:13] Iter: 1747/20000, R4[246/1600], Temp: 0.9428, Energy: -6.284514-0.001981j, Rel_err(%): 1.7433
[2025-06-05 13:32:16] Iter: 1748/20000, R4[247/1600], Temp: 0.9423, Energy: -6.282480-0.000320j, Rel_err(%): 1.7749
[2025-06-05 13:32:20] Iter: 1749/20000, R4[248/1600], Temp: 0.9419, Energy: -6.281859+0.000857j, Rel_err(%): 1.7846
[2025-06-05 13:32:23] Iter: 1750/20000, R4[249/1600], Temp: 0.9414, Energy: -6.283168-0.001100j, Rel_err(%): 1.7642
[2025-06-05 13:32:26] Iter: 1751/20000, R4[250/1600], Temp: 0.9410, Energy: -6.285138+0.001398j, Rel_err(%): 1.7334
[2025-06-05 13:32:29] Iter: 1752/20000, R4[251/1600], Temp: 0.9405, Energy: -6.284773+0.000535j, Rel_err(%): 1.7390
[2025-06-05 13:32:32] Iter: 1753/20000, R4[252/1600], Temp: 0.9400, Energy: -6.284433+0.000027j, Rel_err(%): 1.7443
[2025-06-05 13:32:35] Iter: 1754/20000, R4[253/1600], Temp: 0.9396, Energy: -6.281440-0.000908j, Rel_err(%): 1.7912
[2025-06-05 13:32:38] Iter: 1755/20000, R4[254/1600], Temp: 0.9391, Energy: -6.284305-0.001123j, Rel_err(%): 1.7464
[2025-06-05 13:32:42] Iter: 1756/20000, R4[255/1600], Temp: 0.9386, Energy: -6.282200-0.000788j, Rel_err(%): 1.7793
[2025-06-05 13:32:45] Iter: 1757/20000, R4[256/1600], Temp: 0.9382, Energy: -6.283336+0.000719j, Rel_err(%): 1.7615
[2025-06-05 13:32:48] Iter: 1758/20000, R4[257/1600], Temp: 0.9377, Energy: -6.282092-0.000787j, Rel_err(%): 1.7810
[2025-06-05 13:32:51] Iter: 1759/20000, R4[258/1600], Temp: 0.9372, Energy: -6.282314+0.000715j, Rel_err(%): 1.7775
[2025-06-05 13:32:54] Iter: 1760/20000, R4[259/1600], Temp: 0.9367, Energy: -6.283404+0.000141j, Rel_err(%): 1.7604
[2025-06-05 13:32:57] Iter: 1761/20000, R4[260/1600], Temp: 0.9362, Energy: -6.284094+0.000598j, Rel_err(%): 1.7497
[2025-06-05 13:33:01] Iter: 1762/20000, R4[261/1600], Temp: 0.9358, Energy: -6.282805-0.000021j, Rel_err(%): 1.7698
[2025-06-05 13:33:04] Iter: 1763/20000, R4[262/1600], Temp: 0.9353, Energy: -6.282667+0.000086j, Rel_err(%): 1.7719
[2025-06-05 13:33:07] Iter: 1764/20000, R4[263/1600], Temp: 0.9348, Energy: -6.284807-0.000082j, Rel_err(%): 1.7385
[2025-06-05 13:33:10] Iter: 1765/20000, R4[264/1600], Temp: 0.9343, Energy: -6.283913-0.001128j, Rel_err(%): 1.7526
[2025-06-05 13:33:13] Iter: 1766/20000, R4[265/1600], Temp: 0.9338, Energy: -6.282378+0.000262j, Rel_err(%): 1.7765
[2025-06-05 13:33:16] Iter: 1767/20000, R4[266/1600], Temp: 0.9333, Energy: -6.281716-0.000044j, Rel_err(%): 1.7868
[2025-06-05 13:33:19] Iter: 1768/20000, R4[267/1600], Temp: 0.9328, Energy: -6.284221+0.000895j, Rel_err(%): 1.7477
[2025-06-05 13:33:23] Iter: 1769/20000, R4[268/1600], Temp: 0.9324, Energy: -6.283836-0.000489j, Rel_err(%): 1.7537
[2025-06-05 13:33:26] Iter: 1770/20000, R4[269/1600], Temp: 0.9319, Energy: -6.284078+0.001144j, Rel_err(%): 1.7500
[2025-06-05 13:33:29] Iter: 1771/20000, R4[270/1600], Temp: 0.9314, Energy: -6.282895-0.001214j, Rel_err(%): 1.7685
[2025-06-05 13:33:32] Iter: 1772/20000, R4[271/1600], Temp: 0.9309, Energy: -6.282989+0.000043j, Rel_err(%): 1.7669
[2025-06-05 13:33:35] Iter: 1773/20000, R4[272/1600], Temp: 0.9304, Energy: -6.284379-0.000296j, Rel_err(%): 1.7452
[2025-06-05 13:33:38] Iter: 1774/20000, R4[273/1600], Temp: 0.9299, Energy: -6.283590+0.000271j, Rel_err(%): 1.7575
[2025-06-05 13:33:42] Iter: 1775/20000, R4[274/1600], Temp: 0.9294, Energy: -6.283622+0.000542j, Rel_err(%): 1.7570
[2025-06-05 13:33:45] Iter: 1776/20000, R4[275/1600], Temp: 0.9289, Energy: -6.283066-0.000055j, Rel_err(%): 1.7657
[2025-06-05 13:33:48] Iter: 1777/20000, R4[276/1600], Temp: 0.9284, Energy: -6.283802-0.000697j, Rel_err(%): 1.7542
[2025-06-05 13:33:51] Iter: 1778/20000, R4[277/1600], Temp: 0.9279, Energy: -6.281612-0.000115j, Rel_err(%): 1.7884
[2025-06-05 13:33:54] Iter: 1779/20000, R4[278/1600], Temp: 0.9273, Energy: -6.282681-0.000114j, Rel_err(%): 1.7717
[2025-06-05 13:33:57] Iter: 1780/20000, R4[279/1600], Temp: 0.9268, Energy: -6.283658-0.001801j, Rel_err(%): 1.7567
[2025-06-05 13:34:01] Iter: 1781/20000, R4[280/1600], Temp: 0.9263, Energy: -6.283992-0.001193j, Rel_err(%): 1.7513
[2025-06-05 13:34:04] Iter: 1782/20000, R4[281/1600], Temp: 0.9258, Energy: -6.283896-0.000096j, Rel_err(%): 1.7527
[2025-06-05 13:34:07] Iter: 1783/20000, R4[282/1600], Temp: 0.9253, Energy: -6.283088+0.000433j, Rel_err(%): 1.7654
[2025-06-05 13:34:10] Iter: 1784/20000, R4[283/1600], Temp: 0.9248, Energy: -6.285136+0.000049j, Rel_err(%): 1.7333
[2025-06-05 13:34:13] Iter: 1785/20000, R4[284/1600], Temp: 0.9243, Energy: -6.284703+0.000895j, Rel_err(%): 1.7402
[2025-06-05 13:34:16] Iter: 1786/20000, R4[285/1600], Temp: 0.9237, Energy: -6.284550+0.000667j, Rel_err(%): 1.7425
[2025-06-05 13:34:19] Iter: 1787/20000, R4[286/1600], Temp: 0.9232, Energy: -6.285354-0.000384j, Rel_err(%): 1.7299
[2025-06-05 13:34:23] Iter: 1788/20000, R4[287/1600], Temp: 0.9227, Energy: -6.281523-0.000812j, Rel_err(%): 1.7899
[2025-06-05 13:34:26] Iter: 1789/20000, R4[288/1600], Temp: 0.9222, Energy: -6.283113-0.000792j, Rel_err(%): 1.7650
[2025-06-05 13:34:29] Iter: 1790/20000, R4[289/1600], Temp: 0.9216, Energy: -6.281983-0.000277j, Rel_err(%): 1.7826
[2025-06-05 13:34:32] Iter: 1791/20000, R4[290/1600], Temp: 0.9211, Energy: -6.282566+0.000397j, Rel_err(%): 1.7735
[2025-06-05 13:34:35] Iter: 1792/20000, R4[291/1600], Temp: 0.9206, Energy: -6.281757-0.002149j, Rel_err(%): 1.7865
[2025-06-05 13:34:38] Iter: 1793/20000, R4[292/1600], Temp: 0.9200, Energy: -6.282926+0.000571j, Rel_err(%): 1.7679
[2025-06-05 13:34:42] Iter: 1794/20000, R4[293/1600], Temp: 0.9195, Energy: -6.282370+0.000560j, Rel_err(%): 1.7766
[2025-06-05 13:34:45] Iter: 1795/20000, R4[294/1600], Temp: 0.9190, Energy: -6.285203+0.000105j, Rel_err(%): 1.7323
[2025-06-05 13:34:48] Iter: 1796/20000, R4[295/1600], Temp: 0.9184, Energy: -6.284270+0.000164j, Rel_err(%): 1.7469
[2025-06-05 13:34:51] Iter: 1797/20000, R4[296/1600], Temp: 0.9179, Energy: -6.282791+0.000766j, Rel_err(%): 1.7700
[2025-06-05 13:34:54] Iter: 1798/20000, R4[297/1600], Temp: 0.9174, Energy: -6.281297+0.000501j, Rel_err(%): 1.7934
[2025-06-05 13:34:57] Iter: 1799/20000, R4[298/1600], Temp: 0.9168, Energy: -6.283105+0.000564j, Rel_err(%): 1.7651
[2025-06-05 13:35:00] Iter: 1800/20000, R4[299/1600], Temp: 0.9163, Energy: -6.285245-0.001185j, Rel_err(%): 1.7317
[2025-06-05 13:35:04] Iter: 1801/20000, R4[300/1600], Temp: 0.9157, Energy: -6.285434+0.001887j, Rel_err(%): 1.7289
[2025-06-05 13:35:07] Iter: 1802/20000, R4[301/1600], Temp: 0.9152, Energy: -6.285699+0.000838j, Rel_err(%): 1.7246
[2025-06-05 13:35:10] Iter: 1803/20000, R4[302/1600], Temp: 0.9146, Energy: -6.282653-0.000162j, Rel_err(%): 1.7722
[2025-06-05 13:35:13] Iter: 1804/20000, R4[303/1600], Temp: 0.9141, Energy: -6.283943+0.000566j, Rel_err(%): 1.7520
[2025-06-05 13:35:16] Iter: 1805/20000, R4[304/1600], Temp: 0.9135, Energy: -6.280304-0.000505j, Rel_err(%): 1.8089
[2025-06-05 13:35:19] Iter: 1806/20000, R4[305/1600], Temp: 0.9130, Energy: -6.284058-0.001509j, Rel_err(%): 1.7503
[2025-06-05 13:35:23] Iter: 1807/20000, R4[306/1600], Temp: 0.9124, Energy: -6.284749-0.001344j, Rel_err(%): 1.7395
[2025-06-05 13:35:26] Iter: 1808/20000, R4[307/1600], Temp: 0.9119, Energy: -6.283515+0.000337j, Rel_err(%): 1.7587
[2025-06-05 13:35:29] Iter: 1809/20000, R4[308/1600], Temp: 0.9113, Energy: -6.283413+0.000092j, Rel_err(%): 1.7603
[2025-06-05 13:35:32] Iter: 1810/20000, R4[309/1600], Temp: 0.9108, Energy: -6.282450-0.000830j, Rel_err(%): 1.7754
[2025-06-05 13:35:35] Iter: 1811/20000, R4[310/1600], Temp: 0.9102, Energy: -6.283414-0.000856j, Rel_err(%): 1.7603
[2025-06-05 13:35:38] Iter: 1812/20000, R4[311/1600], Temp: 0.9096, Energy: -6.283504+0.000075j, Rel_err(%): 1.7588
[2025-06-05 13:35:42] Iter: 1813/20000, R4[312/1600], Temp: 0.9091, Energy: -6.282842-0.000021j, Rel_err(%): 1.7692
[2025-06-05 13:35:45] Iter: 1814/20000, R4[313/1600], Temp: 0.9085, Energy: -6.282726+0.000420j, Rel_err(%): 1.7710
[2025-06-05 13:35:48] Iter: 1815/20000, R4[314/1600], Temp: 0.9079, Energy: -6.284169-0.000157j, Rel_err(%): 1.7485
[2025-06-05 13:35:51] Iter: 1816/20000, R4[315/1600], Temp: 0.9074, Energy: -6.282601-0.000458j, Rel_err(%): 1.7730
[2025-06-05 13:35:54] Iter: 1817/20000, R4[316/1600], Temp: 0.9068, Energy: -6.283587-0.002383j, Rel_err(%): 1.7579
[2025-06-05 13:35:57] Iter: 1818/20000, R4[317/1600], Temp: 0.9062, Energy: -6.283240-0.000634j, Rel_err(%): 1.7630
[2025-06-05 13:36:00] Iter: 1819/20000, R4[318/1600], Temp: 0.9057, Energy: -6.282769+0.000099j, Rel_err(%): 1.7703
[2025-06-05 13:36:04] Iter: 1820/20000, R4[319/1600], Temp: 0.9051, Energy: -6.283208+0.001634j, Rel_err(%): 1.7637
[2025-06-05 13:36:07] Iter: 1821/20000, R4[320/1600], Temp: 0.9045, Energy: -6.283546-0.000642j, Rel_err(%): 1.7582
[2025-06-05 13:36:10] Iter: 1822/20000, R4[321/1600], Temp: 0.9039, Energy: -6.284225-0.002300j, Rel_err(%): 1.7480
[2025-06-05 13:36:13] Iter: 1823/20000, R4[322/1600], Temp: 0.9034, Energy: -6.284151-0.000656j, Rel_err(%): 1.7488
[2025-06-05 13:36:16] Iter: 1824/20000, R4[323/1600], Temp: 0.9028, Energy: -6.283696+0.000461j, Rel_err(%): 1.7559
[2025-06-05 13:36:19] Iter: 1825/20000, R4[324/1600], Temp: 0.9022, Energy: -6.282157-0.000254j, Rel_err(%): 1.7799
[2025-06-05 13:36:23] Iter: 1826/20000, R4[325/1600], Temp: 0.9016, Energy: -6.284600+0.000212j, Rel_err(%): 1.7417
[2025-06-05 13:36:26] Iter: 1827/20000, R4[326/1600], Temp: 0.9010, Energy: -6.281289+0.001964j, Rel_err(%): 1.7937
[2025-06-05 13:36:29] Iter: 1828/20000, R4[327/1600], Temp: 0.9004, Energy: -6.281559-0.001221j, Rel_err(%): 1.7894
[2025-06-05 13:36:32] Iter: 1829/20000, R4[328/1600], Temp: 0.8998, Energy: -6.283360-0.002649j, Rel_err(%): 1.7616
[2025-06-05 13:36:35] Iter: 1830/20000, R4[329/1600], Temp: 0.8993, Energy: -6.282587-0.000985j, Rel_err(%): 1.7732
[2025-06-05 13:36:38] Iter: 1831/20000, R4[330/1600], Temp: 0.8987, Energy: -6.283561+0.000442j, Rel_err(%): 1.7580
[2025-06-05 13:36:42] Iter: 1832/20000, R4[331/1600], Temp: 0.8981, Energy: -6.281959+0.001022j, Rel_err(%): 1.7831
[2025-06-05 13:36:45] Iter: 1833/20000, R4[332/1600], Temp: 0.8975, Energy: -6.285500+0.000300j, Rel_err(%): 1.7276
[2025-06-05 13:36:48] Iter: 1834/20000, R4[333/1600], Temp: 0.8969, Energy: -6.283882-0.000952j, Rel_err(%): 1.7530
[2025-06-05 13:36:51] Iter: 1835/20000, R4[334/1600], Temp: 0.8963, Energy: -6.284806-0.002545j, Rel_err(%): 1.7389
[2025-06-05 13:36:54] Iter: 1836/20000, R4[335/1600], Temp: 0.8957, Energy: -6.281438-0.000204j, Rel_err(%): 1.7912
[2025-06-05 13:36:57] Iter: 1837/20000, R4[336/1600], Temp: 0.8951, Energy: -6.282158+0.000910j, Rel_err(%): 1.7800
[2025-06-05 13:37:00] Iter: 1838/20000, R4[337/1600], Temp: 0.8945, Energy: -6.283992+0.000414j, Rel_err(%): 1.7512
[2025-06-05 13:37:04] Iter: 1839/20000, R4[338/1600], Temp: 0.8939, Energy: -6.284694+0.000853j, Rel_err(%): 1.7403
[2025-06-05 13:37:07] Iter: 1840/20000, R4[339/1600], Temp: 0.8933, Energy: -6.284325-0.000134j, Rel_err(%): 1.7460
[2025-06-05 13:37:10] Iter: 1841/20000, R4[340/1600], Temp: 0.8927, Energy: -6.284454+0.000005j, Rel_err(%): 1.7440
[2025-06-05 13:37:13] Iter: 1842/20000, R4[341/1600], Temp: 0.8920, Energy: -6.284491+0.000296j, Rel_err(%): 1.7434
[2025-06-05 13:37:16] Iter: 1843/20000, R4[342/1600], Temp: 0.8914, Energy: -6.285417+0.000637j, Rel_err(%): 1.7290
[2025-06-05 13:37:19] Iter: 1844/20000, R4[343/1600], Temp: 0.8908, Energy: -6.284453+0.000635j, Rel_err(%): 1.7440
[2025-06-05 13:37:23] Iter: 1845/20000, R4[344/1600], Temp: 0.8902, Energy: -6.284055+0.000125j, Rel_err(%): 1.7502
[2025-06-05 13:37:26] Iter: 1846/20000, R4[345/1600], Temp: 0.8896, Energy: -6.283478+0.001014j, Rel_err(%): 1.7593
[2025-06-05 13:37:29] Iter: 1847/20000, R4[346/1600], Temp: 0.8890, Energy: -6.281211-0.000296j, Rel_err(%): 1.7947
[2025-06-05 13:37:32] Iter: 1848/20000, R4[347/1600], Temp: 0.8884, Energy: -6.283005+0.001173j, Rel_err(%): 1.7667
[2025-06-05 13:37:35] Iter: 1849/20000, R4[348/1600], Temp: 0.8877, Energy: -6.283437-0.000054j, Rel_err(%): 1.7599
[2025-06-05 13:37:38] Iter: 1850/20000, R4[349/1600], Temp: 0.8871, Energy: -6.282445-0.000880j, Rel_err(%): 1.7755
[2025-06-05 13:37:41] Iter: 1851/20000, R4[350/1600], Temp: 0.8865, Energy: -6.281449-0.000112j, Rel_err(%): 1.7910
[2025-06-05 13:37:45] Iter: 1852/20000, R4[351/1600], Temp: 0.8859, Energy: -6.282634-0.001029j, Rel_err(%): 1.7725
[2025-06-05 13:37:48] Iter: 1853/20000, R4[352/1600], Temp: 0.8853, Energy: -6.283878-0.001230j, Rel_err(%): 1.7531
[2025-06-05 13:37:51] Iter: 1854/20000, R4[353/1600], Temp: 0.8846, Energy: -6.283243-0.001309j, Rel_err(%): 1.7631
[2025-06-05 13:37:54] Iter: 1855/20000, R4[354/1600], Temp: 0.8840, Energy: -6.283617+0.000178j, Rel_err(%): 1.7571
[2025-06-05 13:37:57] Iter: 1856/20000, R4[355/1600], Temp: 0.8834, Energy: -6.283372+0.000080j, Rel_err(%): 1.7609
[2025-06-05 13:38:00] Iter: 1857/20000, R4[356/1600], Temp: 0.8827, Energy: -6.283867+0.001610j, Rel_err(%): 1.7533
[2025-06-05 13:38:04] Iter: 1858/20000, R4[357/1600], Temp: 0.8821, Energy: -6.283904-0.001630j, Rel_err(%): 1.7528
[2025-06-05 13:38:07] Iter: 1859/20000, R4[358/1600], Temp: 0.8815, Energy: -6.284132-0.000502j, Rel_err(%): 1.7491
[2025-06-05 13:38:10] Iter: 1860/20000, R4[359/1600], Temp: 0.8808, Energy: -6.282121+0.000282j, Rel_err(%): 1.7805
[2025-06-05 13:38:13] Iter: 1861/20000, R4[360/1600], Temp: 0.8802, Energy: -6.283158-0.000427j, Rel_err(%): 1.7643
[2025-06-05 13:38:16] Iter: 1862/20000, R4[361/1600], Temp: 0.8796, Energy: -6.284326+0.000598j, Rel_err(%): 1.7460
[2025-06-05 13:38:19] Iter: 1863/20000, R4[362/1600], Temp: 0.8789, Energy: -6.283700+0.000663j, Rel_err(%): 1.7558
[2025-06-05 13:38:23] Iter: 1864/20000, R4[363/1600], Temp: 0.8783, Energy: -6.284327-0.000588j, Rel_err(%): 1.7460
[2025-06-05 13:38:26] Iter: 1865/20000, R4[364/1600], Temp: 0.8776, Energy: -6.284374-0.002044j, Rel_err(%): 1.7455
[2025-06-05 13:38:29] Iter: 1866/20000, R4[365/1600], Temp: 0.8770, Energy: -6.284745-0.000837j, Rel_err(%): 1.7395
[2025-06-05 13:38:32] Iter: 1867/20000, R4[366/1600], Temp: 0.8764, Energy: -6.282724-0.000273j, Rel_err(%): 1.7710
[2025-06-05 13:38:35] Iter: 1868/20000, R4[367/1600], Temp: 0.8757, Energy: -6.283997+0.000095j, Rel_err(%): 1.7511
[2025-06-05 13:38:38] Iter: 1869/20000, R4[368/1600], Temp: 0.8751, Energy: -6.284899-0.000576j, Rel_err(%): 1.7371
[2025-06-05 13:38:42] Iter: 1870/20000, R4[369/1600], Temp: 0.8744, Energy: -6.283389+0.000407j, Rel_err(%): 1.7607
[2025-06-05 13:38:45] Iter: 1871/20000, R4[370/1600], Temp: 0.8738, Energy: -6.284395-0.000144j, Rel_err(%): 1.7449
[2025-06-05 13:38:48] Iter: 1872/20000, R4[371/1600], Temp: 0.8731, Energy: -6.283455+0.001088j, Rel_err(%): 1.7597
[2025-06-05 13:38:51] Iter: 1873/20000, R4[372/1600], Temp: 0.8724, Energy: -6.284724-0.000943j, Rel_err(%): 1.7398
[2025-06-05 13:38:54] Iter: 1874/20000, R4[373/1600], Temp: 0.8718, Energy: -6.282937-0.000660j, Rel_err(%): 1.7677
[2025-06-05 13:38:57] Iter: 1875/20000, R4[374/1600], Temp: 0.8711, Energy: -6.282394+0.000830j, Rel_err(%): 1.7763
[2025-06-05 13:39:00] Iter: 1876/20000, R4[375/1600], Temp: 0.8705, Energy: -6.281635+0.000042j, Rel_err(%): 1.7881
[2025-06-05 13:39:04] Iter: 1877/20000, R4[376/1600], Temp: 0.8698, Energy: -6.283664+0.000298j, Rel_err(%): 1.7563
[2025-06-05 13:39:07] Iter: 1878/20000, R4[377/1600], Temp: 0.8692, Energy: -6.281770+0.000556j, Rel_err(%): 1.7860
[2025-06-05 13:39:10] Iter: 1879/20000, R4[378/1600], Temp: 0.8685, Energy: -6.285312+0.000260j, Rel_err(%): 1.7306
[2025-06-05 13:39:13] Iter: 1880/20000, R4[379/1600], Temp: 0.8678, Energy: -6.282895+0.000985j, Rel_err(%): 1.7684
[2025-06-05 13:39:16] Iter: 1881/20000, R4[380/1600], Temp: 0.8672, Energy: -6.282944+0.000223j, Rel_err(%): 1.7676
[2025-06-05 13:39:19] Iter: 1882/20000, R4[381/1600], Temp: 0.8665, Energy: -6.283577+0.000142j, Rel_err(%): 1.7577
[2025-06-05 13:39:23] Iter: 1883/20000, R4[382/1600], Temp: 0.8658, Energy: -6.283798-0.000053j, Rel_err(%): 1.7543
[2025-06-05 13:39:26] Iter: 1884/20000, R4[383/1600], Temp: 0.8652, Energy: -6.282940-0.000641j, Rel_err(%): 1.7677
[2025-06-05 13:39:29] Iter: 1885/20000, R4[384/1600], Temp: 0.8645, Energy: -6.282198-0.001806j, Rel_err(%): 1.7795
[2025-06-05 13:39:32] Iter: 1886/20000, R4[385/1600], Temp: 0.8638, Energy: -6.283366+0.000158j, Rel_err(%): 1.7610
[2025-06-05 13:39:35] Iter: 1887/20000, R4[386/1600], Temp: 0.8631, Energy: -6.282838-0.001685j, Rel_err(%): 1.7695
[2025-06-05 13:39:38] Iter: 1888/20000, R4[387/1600], Temp: 0.8625, Energy: -6.282693+0.000231j, Rel_err(%): 1.7715
[2025-06-05 13:39:42] Iter: 1889/20000, R4[388/1600], Temp: 0.8618, Energy: -6.282637+0.000917j, Rel_err(%): 1.7725
[2025-06-05 13:39:45] Iter: 1890/20000, R4[389/1600], Temp: 0.8611, Energy: -6.283666-0.001655j, Rel_err(%): 1.7565
[2025-06-05 13:39:48] Iter: 1891/20000, R4[390/1600], Temp: 0.8604, Energy: -6.284066+0.000126j, Rel_err(%): 1.7501
[2025-06-05 13:39:51] Iter: 1892/20000, R4[391/1600], Temp: 0.8597, Energy: -6.284457+0.000088j, Rel_err(%): 1.7439
[2025-06-05 13:39:54] Iter: 1893/20000, R4[392/1600], Temp: 0.8591, Energy: -6.282728+0.000601j, Rel_err(%): 1.7710
[2025-06-05 13:39:57] Iter: 1894/20000, R4[393/1600], Temp: 0.8584, Energy: -6.283384+0.001536j, Rel_err(%): 1.7609
[2025-06-05 13:40:00] Iter: 1895/20000, R4[394/1600], Temp: 0.8577, Energy: -6.283510-0.000186j, Rel_err(%): 1.7588
[2025-06-05 13:40:04] Iter: 1896/20000, R4[395/1600], Temp: 0.8570, Energy: -6.281661+0.000006j, Rel_err(%): 1.7877
[2025-06-05 13:40:07] Iter: 1897/20000, R4[396/1600], Temp: 0.8563, Energy: -6.281137-0.001478j, Rel_err(%): 1.7960
[2025-06-05 13:40:10] Iter: 1898/20000, R4[397/1600], Temp: 0.8556, Energy: -6.283801+0.000829j, Rel_err(%): 1.7543
[2025-06-05 13:40:13] Iter: 1899/20000, R4[398/1600], Temp: 0.8549, Energy: -6.284774-0.001727j, Rel_err(%): 1.7392
[2025-06-05 13:40:16] Iter: 1900/20000, R4[399/1600], Temp: 0.8542, Energy: -6.283968-0.001255j, Rel_err(%): 1.7517
[2025-06-05 13:40:19] Iter: 1901/20000, R4[400/1600], Temp: 0.8536, Energy: -6.283069+0.000435j, Rel_err(%): 1.7657
[2025-06-05 13:40:23] Iter: 1902/20000, R4[401/1600], Temp: 0.8529, Energy: -6.282484+0.000052j, Rel_err(%): 1.7748
[2025-06-05 13:40:26] Iter: 1903/20000, R4[402/1600], Temp: 0.8522, Energy: -6.283428+0.000213j, Rel_err(%): 1.7600
[2025-06-05 13:40:29] Iter: 1904/20000, R4[403/1600], Temp: 0.8515, Energy: -6.283647+0.000910j, Rel_err(%): 1.7567
[2025-06-05 13:40:32] Iter: 1905/20000, R4[404/1600], Temp: 0.8508, Energy: -6.283130-0.000356j, Rel_err(%): 1.7647
[2025-06-05 13:40:35] Iter: 1906/20000, R4[405/1600], Temp: 0.8501, Energy: -6.283280-0.001279j, Rel_err(%): 1.7625
[2025-06-05 13:40:38] Iter: 1907/20000, R4[406/1600], Temp: 0.8494, Energy: -6.281887+0.000229j, Rel_err(%): 1.7841
[2025-06-05 13:40:42] Iter: 1908/20000, R4[407/1600], Temp: 0.8487, Energy: -6.280598+0.000133j, Rel_err(%): 1.8043
[2025-06-05 13:40:45] Iter: 1909/20000, R4[408/1600], Temp: 0.8480, Energy: -6.281860+0.001091j, Rel_err(%): 1.7846
[2025-06-05 13:40:48] Iter: 1910/20000, R4[409/1600], Temp: 0.8473, Energy: -6.282563+0.000698j, Rel_err(%): 1.7736
[2025-06-05 13:40:51] Iter: 1911/20000, R4[410/1600], Temp: 0.8465, Energy: -6.283430+0.001782j, Rel_err(%): 1.7602
[2025-06-05 13:40:54] Iter: 1912/20000, R4[411/1600], Temp: 0.8458, Energy: -6.282526+0.001235j, Rel_err(%): 1.7742
[2025-06-05 13:40:57] Iter: 1913/20000, R4[412/1600], Temp: 0.8451, Energy: -6.283171-0.000430j, Rel_err(%): 1.7641
[2025-06-05 13:41:00] Iter: 1914/20000, R4[413/1600], Temp: 0.8444, Energy: -6.283315+0.000490j, Rel_err(%): 1.7618
[2025-06-05 13:41:04] Iter: 1915/20000, R4[414/1600], Temp: 0.8437, Energy: -6.283051-0.000695j, Rel_err(%): 1.7660
[2025-06-05 13:41:07] Iter: 1916/20000, R4[415/1600], Temp: 0.8430, Energy: -6.281908+0.002014j, Rel_err(%): 1.7841
[2025-06-05 13:41:10] Iter: 1917/20000, R4[416/1600], Temp: 0.8423, Energy: -6.283457+0.000179j, Rel_err(%): 1.7596
[2025-06-05 13:41:13] Iter: 1918/20000, R4[417/1600], Temp: 0.8416, Energy: -6.284713-0.000357j, Rel_err(%): 1.7400
[2025-06-05 13:41:16] Iter: 1919/20000, R4[418/1600], Temp: 0.8408, Energy: -6.285166-0.000351j, Rel_err(%): 1.7329
[2025-06-05 13:41:19] Iter: 1920/20000, R4[419/1600], Temp: 0.8401, Energy: -6.284398-0.001710j, Rel_err(%): 1.7451
[2025-06-05 13:41:23] Iter: 1921/20000, R4[420/1600], Temp: 0.8394, Energy: -6.284456-0.000272j, Rel_err(%): 1.7440
[2025-06-05 13:41:26] Iter: 1922/20000, R4[421/1600], Temp: 0.8387, Energy: -6.283875+0.000050j, Rel_err(%): 1.7530
[2025-06-05 13:41:29] Iter: 1923/20000, R4[422/1600], Temp: 0.8380, Energy: -6.284585-0.000985j, Rel_err(%): 1.7420
[2025-06-05 13:41:32] Iter: 1924/20000, R4[423/1600], Temp: 0.8372, Energy: -6.282501+0.000262j, Rel_err(%): 1.7745
[2025-06-05 13:41:35] Iter: 1925/20000, R4[424/1600], Temp: 0.8365, Energy: -6.282497+0.000213j, Rel_err(%): 1.7746
[2025-06-05 13:41:38] Iter: 1926/20000, R4[425/1600], Temp: 0.8358, Energy: -6.283546+0.000049j, Rel_err(%): 1.7582
[2025-06-05 13:41:42] Iter: 1927/20000, R4[426/1600], Temp: 0.8351, Energy: -6.280384-0.000487j, Rel_err(%): 1.8077
[2025-06-05 13:41:45] Iter: 1928/20000, R4[427/1600], Temp: 0.8343, Energy: -6.284197+0.000336j, Rel_err(%): 1.7480
[2025-06-05 13:41:48] Iter: 1929/20000, R4[428/1600], Temp: 0.8336, Energy: -6.283193-0.000868j, Rel_err(%): 1.7638
[2025-06-05 13:41:51] Iter: 1930/20000, R4[429/1600], Temp: 0.8329, Energy: -6.283758+0.002076j, Rel_err(%): 1.7552
[2025-06-05 13:41:54] Iter: 1931/20000, R4[430/1600], Temp: 0.8321, Energy: -6.283154+0.000975j, Rel_err(%): 1.7644
[2025-06-05 13:41:57] Iter: 1932/20000, R4[431/1600], Temp: 0.8314, Energy: -6.283748+0.000222j, Rel_err(%): 1.7550
[2025-06-05 13:42:00] Iter: 1933/20000, R4[432/1600], Temp: 0.8307, Energy: -6.281431+0.000252j, Rel_err(%): 1.7913
[2025-06-05 13:42:04] Iter: 1934/20000, R4[433/1600], Temp: 0.8299, Energy: -6.282435-0.001033j, Rel_err(%): 1.7756
[2025-06-05 13:42:07] Iter: 1935/20000, R4[434/1600], Temp: 0.8292, Energy: -6.283886+0.000075j, Rel_err(%): 1.7529
[2025-06-05 13:42:10] Iter: 1936/20000, R4[435/1600], Temp: 0.8284, Energy: -6.283394+0.000696j, Rel_err(%): 1.7606
[2025-06-05 13:42:13] Iter: 1937/20000, R4[436/1600], Temp: 0.8277, Energy: -6.283496+0.000966j, Rel_err(%): 1.7590
[2025-06-05 13:42:16] Iter: 1938/20000, R4[437/1600], Temp: 0.8270, Energy: -6.282761-0.000599j, Rel_err(%): 1.7705
[2025-06-05 13:42:19] Iter: 1939/20000, R4[438/1600], Temp: 0.8262, Energy: -6.283282-0.000315j, Rel_err(%): 1.7623
[2025-06-05 13:42:23] Iter: 1940/20000, R4[439/1600], Temp: 0.8255, Energy: -6.282065-0.000693j, Rel_err(%): 1.7814
[2025-06-05 13:42:26] Iter: 1941/20000, R4[440/1600], Temp: 0.8247, Energy: -6.283866-0.003280j, Rel_err(%): 1.7539
[2025-06-05 13:42:29] Iter: 1942/20000, R4[441/1600], Temp: 0.8240, Energy: -6.283398+0.001499j, Rel_err(%): 1.7607
[2025-06-05 13:42:32] Iter: 1943/20000, R4[442/1600], Temp: 0.8232, Energy: -6.284473+0.000165j, Rel_err(%): 1.7437
[2025-06-05 13:42:35] Iter: 1944/20000, R4[443/1600], Temp: 0.8225, Energy: -6.285701-0.000023j, Rel_err(%): 1.7245
[2025-06-05 13:42:38] Iter: 1945/20000, R4[444/1600], Temp: 0.8217, Energy: -6.284495-0.000426j, Rel_err(%): 1.7434
[2025-06-05 13:42:42] Iter: 1946/20000, R4[445/1600], Temp: 0.8210, Energy: -6.284069+0.000441j, Rel_err(%): 1.7500
[2025-06-05 13:42:45] Iter: 1947/20000, R4[446/1600], Temp: 0.8202, Energy: -6.282970+0.000681j, Rel_err(%): 1.7672
[2025-06-05 13:42:48] Iter: 1948/20000, R4[447/1600], Temp: 0.8195, Energy: -6.282825+0.000017j, Rel_err(%): 1.7695
[2025-06-05 13:42:51] Iter: 1949/20000, R4[448/1600], Temp: 0.8187, Energy: -6.283613+0.000009j, Rel_err(%): 1.7571
[2025-06-05 13:42:54] Iter: 1950/20000, R4[449/1600], Temp: 0.8180, Energy: -6.284112+0.000683j, Rel_err(%): 1.7494
[2025-06-05 13:42:57] Iter: 1951/20000, R4[450/1600], Temp: 0.8172, Energy: -6.286196+0.000743j, Rel_err(%): 1.7168
[2025-06-05 13:43:01] Iter: 1952/20000, R4[451/1600], Temp: 0.8164, Energy: -6.286116-0.000939j, Rel_err(%): 1.7181
[2025-06-05 13:43:04] Iter: 1953/20000, R4[452/1600], Temp: 0.8157, Energy: -6.284776-0.000838j, Rel_err(%): 1.7390
[2025-06-05 13:43:07] Iter: 1954/20000, R4[453/1600], Temp: 0.8149, Energy: -6.286249-0.000516j, Rel_err(%): 1.7159
[2025-06-05 13:43:10] Iter: 1955/20000, R4[454/1600], Temp: 0.8142, Energy: -6.285262-0.000287j, Rel_err(%): 1.7314
[2025-06-05 13:43:13] Iter: 1956/20000, R4[455/1600], Temp: 0.8134, Energy: -6.283507+0.000926j, Rel_err(%): 1.7589
[2025-06-05 13:43:16] Iter: 1957/20000, R4[456/1600], Temp: 0.8126, Energy: -6.285408+0.001153j, Rel_err(%): 1.7292
[2025-06-05 13:43:19] Iter: 1958/20000, R4[457/1600], Temp: 0.8119, Energy: -6.283982-0.000720j, Rel_err(%): 1.7514
[2025-06-05 13:43:23] Iter: 1959/20000, R4[458/1600], Temp: 0.8111, Energy: -6.284441-0.000654j, Rel_err(%): 1.7442
[2025-06-05 13:43:26] Iter: 1960/20000, R4[459/1600], Temp: 0.8103, Energy: -6.285598-0.001357j, Rel_err(%): 1.7262
[2025-06-05 13:43:29] Iter: 1961/20000, R4[460/1600], Temp: 0.8095, Energy: -6.285084-0.000483j, Rel_err(%): 1.7342
[2025-06-05 13:43:32] Iter: 1962/20000, R4[461/1600], Temp: 0.8088, Energy: -6.285307-0.001132j, Rel_err(%): 1.7308
[2025-06-05 13:43:35] Iter: 1963/20000, R4[462/1600], Temp: 0.8080, Energy: -6.285684+0.001065j, Rel_err(%): 1.7249
[2025-06-05 13:43:38] Iter: 1964/20000, R4[463/1600], Temp: 0.8072, Energy: -6.283406+0.001573j, Rel_err(%): 1.7606
[2025-06-05 13:43:42] Iter: 1965/20000, R4[464/1600], Temp: 0.8065, Energy: -6.282995+0.000035j, Rel_err(%): 1.7668
[2025-06-05 13:43:45] Iter: 1966/20000, R4[465/1600], Temp: 0.8057, Energy: -6.281752+0.000077j, Rel_err(%): 1.7862
[2025-06-05 13:43:48] Iter: 1967/20000, R4[466/1600], Temp: 0.8049, Energy: -6.283094+0.001286j, Rel_err(%): 1.7654
[2025-06-05 13:43:51] Iter: 1968/20000, R4[467/1600], Temp: 0.8041, Energy: -6.283089-0.001037j, Rel_err(%): 1.7654
[2025-06-05 13:43:54] Iter: 1969/20000, R4[468/1600], Temp: 0.8033, Energy: -6.285421+0.000099j, Rel_err(%): 1.7289
[2025-06-05 13:43:57] Iter: 1970/20000, R4[469/1600], Temp: 0.8026, Energy: -6.284526+0.001286j, Rel_err(%): 1.7430
[2025-06-05 13:44:01] Iter: 1971/20000, R4[470/1600], Temp: 0.8018, Energy: -6.283979-0.000957j, Rel_err(%): 1.7515
[2025-06-05 13:44:04] Iter: 1972/20000, R4[471/1600], Temp: 0.8010, Energy: -6.285610-0.000342j, Rel_err(%): 1.7259
[2025-06-05 13:44:07] Iter: 1973/20000, R4[472/1600], Temp: 0.8002, Energy: -6.283947-0.001026j, Rel_err(%): 1.7520
[2025-06-05 13:44:10] Iter: 1974/20000, R4[473/1600], Temp: 0.7994, Energy: -6.283184-0.000388j, Rel_err(%): 1.7639
[2025-06-05 13:44:13] Iter: 1975/20000, R4[474/1600], Temp: 0.7986, Energy: -6.283736+0.000715j, Rel_err(%): 1.7553
[2025-06-05 13:44:16] Iter: 1976/20000, R4[475/1600], Temp: 0.7978, Energy: -6.286296-0.000196j, Rel_err(%): 1.7152
[2025-06-05 13:44:19] Iter: 1977/20000, R4[476/1600], Temp: 0.7971, Energy: -6.283593-0.001403j, Rel_err(%): 1.7576
[2025-06-05 13:44:23] Iter: 1978/20000, R4[477/1600], Temp: 0.7963, Energy: -6.283691-0.000589j, Rel_err(%): 1.7560
[2025-06-05 13:44:26] Iter: 1979/20000, R4[478/1600], Temp: 0.7955, Energy: -6.283773-0.000397j, Rel_err(%): 1.7547
[2025-06-05 13:44:29] Iter: 1980/20000, R4[479/1600], Temp: 0.7947, Energy: -6.284653-0.001811j, Rel_err(%): 1.7411
[2025-06-05 13:44:32] Iter: 1981/20000, R4[480/1600], Temp: 0.7939, Energy: -6.283994+0.001370j, Rel_err(%): 1.7513
[2025-06-05 13:44:35] Iter: 1982/20000, R4[481/1600], Temp: 0.7931, Energy: -6.281485+0.000781j, Rel_err(%): 1.7905
[2025-06-05 13:44:38] Iter: 1983/20000, R4[482/1600], Temp: 0.7923, Energy: -6.282754+0.000697j, Rel_err(%): 1.7706
[2025-06-05 13:44:42] Iter: 1984/20000, R4[483/1600], Temp: 0.7915, Energy: -6.285234+0.000514j, Rel_err(%): 1.7318
[2025-06-05 13:44:45] Iter: 1985/20000, R4[484/1600], Temp: 0.7907, Energy: -6.285054-0.000504j, Rel_err(%): 1.7346
[2025-06-05 13:44:48] Iter: 1986/20000, R4[485/1600], Temp: 0.7899, Energy: -6.282880-0.001085j, Rel_err(%): 1.7687
[2025-06-05 13:44:51] Iter: 1987/20000, R4[486/1600], Temp: 0.7891, Energy: -6.285720-0.001036j, Rel_err(%): 1.7243
[2025-06-05 13:44:54] Iter: 1988/20000, R4[487/1600], Temp: 0.7883, Energy: -6.285415+0.001280j, Rel_err(%): 1.7291
[2025-06-05 13:44:57] Iter: 1989/20000, R4[488/1600], Temp: 0.7875, Energy: -6.284535+0.001193j, Rel_err(%): 1.7428
[2025-06-05 13:45:00] Iter: 1990/20000, R4[489/1600], Temp: 0.7867, Energy: -6.284180-0.000118j, Rel_err(%): 1.7483
[2025-06-05 13:45:04] Iter: 1991/20000, R4[490/1600], Temp: 0.7859, Energy: -6.285023-0.000940j, Rel_err(%): 1.7352
[2025-06-05 13:45:07] Iter: 1992/20000, R4[491/1600], Temp: 0.7851, Energy: -6.283558+0.000305j, Rel_err(%): 1.7580
[2025-06-05 13:45:10] Iter: 1993/20000, R4[492/1600], Temp: 0.7843, Energy: -6.284427-0.001279j, Rel_err(%): 1.7445
[2025-06-05 13:45:13] Iter: 1994/20000, R4[493/1600], Temp: 0.7835, Energy: -6.282189+0.001959j, Rel_err(%): 1.7797
[2025-06-05 13:45:16] Iter: 1995/20000, R4[494/1600], Temp: 0.7827, Energy: -6.283577+0.000595j, Rel_err(%): 1.7577
[2025-06-05 13:45:19] Iter: 1996/20000, R4[495/1600], Temp: 0.7819, Energy: -6.282047+0.001342j, Rel_err(%): 1.7817
[2025-06-05 13:45:23] Iter: 1997/20000, R4[496/1600], Temp: 0.7810, Energy: -6.283454+0.001060j, Rel_err(%): 1.7597
[2025-06-05 13:45:26] Iter: 1998/20000, R4[497/1600], Temp: 0.7802, Energy: -6.284046-0.001245j, Rel_err(%): 1.7505
[2025-06-05 13:45:29] Iter: 1999/20000, R4[498/1600], Temp: 0.7794, Energy: -6.283729+0.000768j, Rel_err(%): 1.7554
[2025-06-05 13:45:32] Iter: 2000/20000, R4[499/1600], Temp: 0.7786, Energy: -6.285069+0.000979j, Rel_err(%): 1.7345
[2025-06-05 13:45:35] Iter: 2001/20000, R4[500/1600], Temp: 0.7778, Energy: -6.285490+0.000703j, Rel_err(%): 1.7278
[2025-06-05 13:45:38] Iter: 2002/20000, R4[501/1600], Temp: 0.7770, Energy: -6.283570-0.000433j, Rel_err(%): 1.7578
[2025-06-05 13:45:42] Iter: 2003/20000, R4[502/1600], Temp: 0.7762, Energy: -6.284576-0.001951j, Rel_err(%): 1.7424
[2025-06-05 13:45:45] Iter: 2004/20000, R4[503/1600], Temp: 0.7753, Energy: -6.284584-0.001616j, Rel_err(%): 1.7421
[2025-06-05 13:45:48] Iter: 2005/20000, R4[504/1600], Temp: 0.7745, Energy: -6.284049-0.000863j, Rel_err(%): 1.7504
[2025-06-05 13:45:51] Iter: 2006/20000, R4[505/1600], Temp: 0.7737, Energy: -6.283643+0.000206j, Rel_err(%): 1.7567
[2025-06-05 13:45:54] Iter: 2007/20000, R4[506/1600], Temp: 0.7729, Energy: -6.285672-0.000428j, Rel_err(%): 1.7250
[2025-06-05 13:45:57] Iter: 2008/20000, R4[507/1600], Temp: 0.7720, Energy: -6.284955+0.001741j, Rel_err(%): 1.7364
[2025-06-05 13:46:00] Iter: 2009/20000, R4[508/1600], Temp: 0.7712, Energy: -6.282176+0.000512j, Rel_err(%): 1.7796
[2025-06-05 13:46:04] Iter: 2010/20000, R4[509/1600], Temp: 0.7704, Energy: -6.282994+0.001649j, Rel_err(%): 1.7670
[2025-06-05 13:46:07] Iter: 2011/20000, R4[510/1600], Temp: 0.7696, Energy: -6.282434+0.001473j, Rel_err(%): 1.7757
[2025-06-05 13:46:10] Iter: 2012/20000, R4[511/1600], Temp: 0.7687, Energy: -6.284695+0.001790j, Rel_err(%): 1.7404
[2025-06-05 13:46:13] Iter: 2013/20000, R4[512/1600], Temp: 0.7679, Energy: -6.283180+0.001351j, Rel_err(%): 1.7640
[2025-06-05 13:46:16] Iter: 2014/20000, R4[513/1600], Temp: 0.7671, Energy: -6.282784+0.000939j, Rel_err(%): 1.7702
[2025-06-05 13:46:19] Iter: 2015/20000, R4[514/1600], Temp: 0.7663, Energy: -6.285219+0.002205j, Rel_err(%): 1.7324
[2025-06-05 13:46:23] Iter: 2016/20000, R4[515/1600], Temp: 0.7654, Energy: -6.284206+0.000573j, Rel_err(%): 1.7479
[2025-06-05 13:46:26] Iter: 2017/20000, R4[516/1600], Temp: 0.7646, Energy: -6.284486+0.000232j, Rel_err(%): 1.7435
[2025-06-05 13:46:29] Iter: 2018/20000, R4[517/1600], Temp: 0.7638, Energy: -6.283936-0.000634j, Rel_err(%): 1.7521
[2025-06-05 13:46:32] Iter: 2019/20000, R4[518/1600], Temp: 0.7629, Energy: -6.284657-0.000731j, Rel_err(%): 1.7409
[2025-06-05 13:46:35] Iter: 2020/20000, R4[519/1600], Temp: 0.7621, Energy: -6.284535+0.000594j, Rel_err(%): 1.7428
[2025-06-05 13:46:38] Iter: 2021/20000, R4[520/1600], Temp: 0.7612, Energy: -6.285613+0.000093j, Rel_err(%): 1.7259
[2025-06-05 13:46:42] Iter: 2022/20000, R4[521/1600], Temp: 0.7604, Energy: -6.286230-0.000570j, Rel_err(%): 1.7162
[2025-06-05 13:46:45] Iter: 2023/20000, R4[522/1600], Temp: 0.7596, Energy: -6.285234-0.000258j, Rel_err(%): 1.7318
[2025-06-05 13:46:48] Iter: 2024/20000, R4[523/1600], Temp: 0.7587, Energy: -6.285511-0.000394j, Rel_err(%): 1.7275
[2025-06-05 13:46:51] Iter: 2025/20000, R4[524/1600], Temp: 0.7579, Energy: -6.285162-0.001635j, Rel_err(%): 1.7331
[2025-06-05 13:46:54] Iter: 2026/20000, R4[525/1600], Temp: 0.7571, Energy: -6.283953-0.000057j, Rel_err(%): 1.7518
[2025-06-05 13:46:57] Iter: 2027/20000, R4[526/1600], Temp: 0.7562, Energy: -6.285424+0.000158j, Rel_err(%): 1.7288
[2025-06-05 13:47:00] Iter: 2028/20000, R4[527/1600], Temp: 0.7554, Energy: -6.285353+0.000987j, Rel_err(%): 1.7300
[2025-06-05 13:47:04] Iter: 2029/20000, R4[528/1600], Temp: 0.7545, Energy: -6.284118-0.000589j, Rel_err(%): 1.7493
[2025-06-05 13:47:07] Iter: 2030/20000, R4[529/1600], Temp: 0.7537, Energy: -6.285487+0.000791j, Rel_err(%): 1.7279
[2025-06-05 13:47:10] Iter: 2031/20000, R4[530/1600], Temp: 0.7528, Energy: -6.283294+0.001743j, Rel_err(%): 1.7623
[2025-06-05 13:47:13] Iter: 2032/20000, R4[531/1600], Temp: 0.7520, Energy: -6.284557+0.000778j, Rel_err(%): 1.7424
[2025-06-05 13:47:16] Iter: 2033/20000, R4[532/1600], Temp: 0.7511, Energy: -6.282796+0.000811j, Rel_err(%): 1.7700
[2025-06-05 13:47:19] Iter: 2034/20000, R4[533/1600], Temp: 0.7503, Energy: -6.283040-0.000191j, Rel_err(%): 1.7661
[2025-06-05 13:47:23] Iter: 2035/20000, R4[534/1600], Temp: 0.7494, Energy: -6.284892-0.000073j, Rel_err(%): 1.7371
[2025-06-05 13:47:26] Iter: 2036/20000, R4[535/1600], Temp: 0.7486, Energy: -6.283094+0.001695j, Rel_err(%): 1.7655
[2025-06-05 13:47:29] Iter: 2037/20000, R4[536/1600], Temp: 0.7477, Energy: -6.282600-0.001463j, Rel_err(%): 1.7731
[2025-06-05 13:47:32] Iter: 2038/20000, R4[537/1600], Temp: 0.7469, Energy: -6.282916+0.000872j, Rel_err(%): 1.7681
[2025-06-05 13:47:35] Iter: 2039/20000, R4[538/1600], Temp: 0.7460, Energy: -6.283050+0.000233j, Rel_err(%): 1.7659
[2025-06-05 13:47:38] Iter: 2040/20000, R4[539/1600], Temp: 0.7452, Energy: -6.284355+0.000395j, Rel_err(%): 1.7456
[2025-06-05 13:47:42] Iter: 2041/20000, R4[540/1600], Temp: 0.7443, Energy: -6.285036+0.000186j, Rel_err(%): 1.7349
[2025-06-05 13:47:45] Iter: 2042/20000, R4[541/1600], Temp: 0.7435, Energy: -6.284769+0.000903j, Rel_err(%): 1.7391
[2025-06-05 13:47:48] Iter: 2043/20000, R4[542/1600], Temp: 0.7426, Energy: -6.283766-0.000327j, Rel_err(%): 1.7548
[2025-06-05 13:47:51] Iter: 2044/20000, R4[543/1600], Temp: 0.7417, Energy: -6.284947+0.000305j, Rel_err(%): 1.7363
[2025-06-05 13:47:54] Iter: 2045/20000, R4[544/1600], Temp: 0.7409, Energy: -6.283625+0.000379j, Rel_err(%): 1.7570
[2025-06-05 13:47:57] Iter: 2046/20000, R4[545/1600], Temp: 0.7400, Energy: -6.283475+0.000606j, Rel_err(%): 1.7593
[2025-06-05 13:48:00] Iter: 2047/20000, R4[546/1600], Temp: 0.7392, Energy: -6.285726-0.000649j, Rel_err(%): 1.7241
[2025-06-05 13:48:04] Iter: 2048/20000, R4[547/1600], Temp: 0.7383, Energy: -6.285099+0.000583j, Rel_err(%): 1.7339
[2025-06-05 13:48:07] Iter: 2049/20000, R4[548/1600], Temp: 0.7374, Energy: -6.282617-0.000319j, Rel_err(%): 1.7727
[2025-06-05 13:48:10] Iter: 2050/20000, R4[549/1600], Temp: 0.7366, Energy: -6.284075+0.001043j, Rel_err(%): 1.7500
[2025-06-05 13:48:13] Iter: 2051/20000, R4[550/1600], Temp: 0.7357, Energy: -6.285785+0.002145j, Rel_err(%): 1.7235
[2025-06-05 13:48:16] Iter: 2052/20000, R4[551/1600], Temp: 0.7348, Energy: -6.284897-0.000422j, Rel_err(%): 1.7371
[2025-06-05 13:48:19] Iter: 2053/20000, R4[552/1600], Temp: 0.7340, Energy: -6.285221+0.000011j, Rel_err(%): 1.7320
[2025-06-05 13:48:23] Iter: 2054/20000, R4[553/1600], Temp: 0.7331, Energy: -6.285691+0.000735j, Rel_err(%): 1.7247
[2025-06-05 13:48:26] Iter: 2055/20000, R4[554/1600], Temp: 0.7322, Energy: -6.283215-0.002616j, Rel_err(%): 1.7638
[2025-06-05 13:48:29] Iter: 2056/20000, R4[555/1600], Temp: 0.7314, Energy: -6.283665-0.000377j, Rel_err(%): 1.7563
[2025-06-05 13:48:32] Iter: 2057/20000, R4[556/1600], Temp: 0.7305, Energy: -6.284559-0.000129j, Rel_err(%): 1.7423
[2025-06-05 13:48:35] Iter: 2058/20000, R4[557/1600], Temp: 0.7296, Energy: -6.283788-0.000675j, Rel_err(%): 1.7544
[2025-06-05 13:48:38] Iter: 2059/20000, R4[558/1600], Temp: 0.7287, Energy: -6.285328-0.002441j, Rel_err(%): 1.7308
[2025-06-05 13:48:42] Iter: 2060/20000, R4[559/1600], Temp: 0.7279, Energy: -6.282788-0.000063j, Rel_err(%): 1.7700
[2025-06-05 13:48:45] Iter: 2061/20000, R4[560/1600], Temp: 0.7270, Energy: -6.285227-0.000126j, Rel_err(%): 1.7319
[2025-06-05 13:48:48] Iter: 2062/20000, R4[561/1600], Temp: 0.7261, Energy: -6.284198+0.000087j, Rel_err(%): 1.7480
[2025-06-05 13:48:51] Iter: 2063/20000, R4[562/1600], Temp: 0.7252, Energy: -6.284049-0.000154j, Rel_err(%): 1.7503
[2025-06-05 13:48:54] Iter: 2064/20000, R4[563/1600], Temp: 0.7244, Energy: -6.284651-0.000397j, Rel_err(%): 1.7409
[2025-06-05 13:48:57] Iter: 2065/20000, R4[564/1600], Temp: 0.7235, Energy: -6.285229-0.000720j, Rel_err(%): 1.7319
[2025-06-05 13:49:00] Iter: 2066/20000, R4[565/1600], Temp: 0.7226, Energy: -6.284275-0.000134j, Rel_err(%): 1.7468
[2025-06-05 13:49:04] Iter: 2067/20000, R4[566/1600], Temp: 0.7217, Energy: -6.283793-0.000605j, Rel_err(%): 1.7544
[2025-06-05 13:49:07] Iter: 2068/20000, R4[567/1600], Temp: 0.7209, Energy: -6.284623-0.000595j, Rel_err(%): 1.7414
[2025-06-05 13:49:10] Iter: 2069/20000, R4[568/1600], Temp: 0.7200, Energy: -6.284834+0.000780j, Rel_err(%): 1.7381
[2025-06-05 13:49:13] Iter: 2070/20000, R4[569/1600], Temp: 0.7191, Energy: -6.285565-0.001808j, Rel_err(%): 1.7269
[2025-06-05 13:49:16] Iter: 2071/20000, R4[570/1600], Temp: 0.7182, Energy: -6.284541-0.002055j, Rel_err(%): 1.7429
[2025-06-05 13:49:19] Iter: 2072/20000, R4[571/1600], Temp: 0.7173, Energy: -6.283835+0.000221j, Rel_err(%): 1.7537
[2025-06-05 13:49:23] Iter: 2073/20000, R4[572/1600], Temp: 0.7164, Energy: -6.284450+0.001532j, Rel_err(%): 1.7442
[2025-06-05 13:49:26] Iter: 2074/20000, R4[573/1600], Temp: 0.7156, Energy: -6.284971-0.001685j, Rel_err(%): 1.7361
[2025-06-05 13:49:29] Iter: 2075/20000, R4[574/1600], Temp: 0.7147, Energy: -6.283388-0.001581j, Rel_err(%): 1.7608
[2025-06-05 13:49:32] Iter: 2076/20000, R4[575/1600], Temp: 0.7138, Energy: -6.283726-0.000616j, Rel_err(%): 1.7554
[2025-06-05 13:49:35] Iter: 2077/20000, R4[576/1600], Temp: 0.7129, Energy: -6.285336+0.000117j, Rel_err(%): 1.7302
[2025-06-05 13:49:38] Iter: 2078/20000, R4[577/1600], Temp: 0.7120, Energy: -6.284721+0.000187j, Rel_err(%): 1.7398
[2025-06-05 13:49:42] Iter: 2079/20000, R4[578/1600], Temp: 0.7111, Energy: -6.283913+0.000042j, Rel_err(%): 1.7525
[2025-06-05 13:49:45] Iter: 2080/20000, R4[579/1600], Temp: 0.7102, Energy: -6.283612+0.001697j, Rel_err(%): 1.7574
[2025-06-05 13:49:48] Iter: 2081/20000, R4[580/1600], Temp: 0.7093, Energy: -6.283298+0.001001j, Rel_err(%): 1.7621
