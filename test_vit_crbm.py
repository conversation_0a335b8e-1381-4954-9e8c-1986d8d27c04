#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试ViT_cRBM模型的简单脚本
"""

import os
import sys
sys.path.append(os.path.abspath('.'))

import jax
import jax.numpy as jnp
import numpy as np

# 导入新模型
from src.models.ViT_cRBM import ViT_cRBM, ModelConfig, TrainingConfig

def test_vit_crbm_model():
    """测试ViT_cRBM模型的基本功能"""
    print("测试ViT_cRBM模型...")
    
    # 设置测试参数
    Lx, Ly = 2, 2  # 小尺寸用于测试
    N = 2 * Lx * Ly  # 蜂窝晶格的总格点数
    batch_size = 4
    
    print(f"晶格尺寸: Lx={Lx}, Ly={Ly}")
    print(f"总格点数: N={N}")
    print(f"批次大小: {batch_size}")
    
    # 创建模型配置
    config = ModelConfig()
    print(f"模型配置:")
    print(f"  - cRBM alpha: {config.alpha}")
    print(f"  - ViT layers: {config.num_layers}")
    print(f"  - d_model: {config.d_model}")
    print(f"  - heads: {config.heads}")
    print(f"  - patch_size: {config.patch_size}")
    print(f"  - param_dtype: {config.param_dtype}")
    
    # 创建模型实例
    model = ViT_cRBM(
        Lx=Lx,
        Ly=Ly,
        num_layers=config.num_layers,
        d_model=config.d_model,
        heads=config.heads,
        patch_size=config.patch_size,
        alpha=config.alpha,
        param_dtype=getattr(np, config.param_dtype),
        use_hidden_bias=config.use_hidden_bias,
        use_visible_bias=config.use_visible_bias
    )
    
    print(f"模型创建成功!")
    
    # 创建随机输入（自旋构型）
    key = jax.random.PRNGKey(42)
    input_data = jax.random.choice(key, jnp.array([-1, 1]), shape=(batch_size, N))
    print(f"输入数据形状: {input_data.shape}")
    print(f"输入数据类型: {input_data.dtype}")
    
    # 初始化模型参数
    params = model.init(key, input_data)
    print(f"模型参数初始化成功!")
    
    # 前向传播
    try:
        output = model.apply(params, input_data)
        print(f"前向传播成功!")
        print(f"输出形状: {output.shape}")
        print(f"输出类型: {output.dtype}")
        print(f"输出样例: {output[:2]}")
        
        # 检查输出是否为复数
        if jnp.iscomplexobj(output):
            print(f"✓ 输出是复数类型")
            print(f"实部范围: [{jnp.min(jnp.real(output)):.4f}, {jnp.max(jnp.real(output)):.4f}]")
            print(f"虚部范围: [{jnp.min(jnp.imag(output)):.4f}, {jnp.max(jnp.imag(output)):.4f}]")
        else:
            print(f"✗ 输出不是复数类型")
            
        return True
        
    except Exception as e:
        print(f"前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_dimensions():
    """测试特征维度计算"""
    print("\n测试特征维度计算...")
    
    Lx, Ly = 2, 2
    N = 2 * Lx * Ly
    
    # 根据cRBM的逻辑计算增强特征维度
    # 原始自旋: N
    # x方向关联子: 2 * Lx * Ly
    # y方向关联子（删除一半）: Lx * Ly
    # 总计: N + 2*Lx*Ly + Lx*Ly = N + 3*Lx*Ly = 4*Lx*Ly + 3*Lx*Ly = 7*Lx*Ly
    correlator_dim = 3 * Lx * Ly
    total_input_dim = N + correlator_dim
    
    print(f"原始自旋维度: {N}")
    print(f"关联子维度: {correlator_dim}")
    print(f"总输入维度: {total_input_dim}")
    
    # cRBM特征提取后的维度
    alpha = 2
    enhanced_feature_dim = int(alpha * total_input_dim)
    print(f"增强特征维度 (alpha={alpha}): {enhanced_feature_dim}")
    
    # 检查是否能被patch_size整除
    patch_size = 2
    if enhanced_feature_dim % patch_size == 0:
        n_patches = enhanced_feature_dim // patch_size
        print(f"✓ 特征维度能被patch_size={patch_size}整除")
        print(f"patch数量: {n_patches}")
    else:
        print(f"✗ 特征维度不能被patch_size={patch_size}整除")
        print(f"建议调整alpha或patch_size")

if __name__ == "__main__":
    print("="*60)
    print("ViT_cRBM模型测试")
    print("="*60)
    
    # 测试特征维度
    test_feature_dimensions()
    
    print("\n" + "="*60)
    
    # 测试模型
    success = test_vit_crbm_model()
    
    print("\n" + "="*60)
    if success:
        print("✓ 所有测试通过!")
        print("ViT_cRBM模型可以正常使用")
    else:
        print("✗ 测试失败!")
        print("需要修复模型实现")
    print("="*60)
