#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Kitaev模型训练脚本
"""

# 在开始添加环境配置
import os
os.environ["XLA_FLAGS"] = "--xla_gpu_cuda_data_dir=/usr/local/cuda"
os.environ["NETKET_EXPERIMENTAL_SHARDING"] = "1"  # 启用NetKet的分片功能
os.environ["XLA_PYTHON_CLIENT_ALLOCATOR"] = "platform"  # 使用平台特定的内存分配器
os.environ["XLA_PYTHON_CLIENT_PREALLOCATE"] = "false"  # 禁用预分配
os.environ["JAX_PLATFORM_NAME"] = "gpu"

# 添加上级目录到Python路径
import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import time
import json
import datetime
import jax
import numpy as np
import jax.numpy as jnp
import netket as nk

# 导入模型和配置
from src.models import (
    cRBM, ViTFNQS,
    cRBM_ModelConfig, cRBM_TrainingConfig,
    ViT_ModelConfig, ViT_TrainingConfig
)
from src.runner import KitaevRunner
from src.utils.logging import log_message

def run_simulation(model_type, Lx, Ly, spin, Kx, Ky, Kz, J, hx, hy, hz, Lambda, reference_energy):
    """
    执行单个Kitaev模型模拟
    
    Args:
        model_type: 模型类型 ("cRBM" 或 "ViT")
        Lx, Ly: 晶格尺寸
        spin: 自旋大小
        Kx, Ky, Kz: Kitaev相互作用强度
        J: Heisenberg相互作用强度
        hx, hy, hz: [111]方向磁场
        Lambda: plaquette算符的拉格朗日乘子
        reference_energy: 参考能量
    """
    try:
        # 根据模型类型选择配置
        if model_type.lower() == "crbm":
            ModelConfig = cRBM_ModelConfig
            TrainingConfig = cRBM_TrainingConfig
            model_class = cRBM
        elif model_type.lower() == "vit":
            ModelConfig = ViT_ModelConfig
            TrainingConfig = ViT_TrainingConfig
            model_class = ViTFNQS
        else:
            raise ValueError(f"不支持的模型类型: {model_type}。请使用 'cRBM' 或 'ViT'")
        
        # 创建运行器并执行模拟
        runner = KitaevRunner(
            Lx, Ly, Kx, Ky, Kz, J, hx, hy, hz, Lambda,
            model_type=model_type,
            model_class=model_class,
            model_config=ModelConfig,
            training_config=TrainingConfig,
            reference_energy=reference_energy
        )
        runner.setup_model()
        runner.run()
        
        log_message(runner.energy_log, f"Simulation completed successfully for parameters:")
        log_message(runner.energy_log, f"Model={model_type}, Lx={Lx}, Ly={Ly}, spin={spin}")
        log_message(runner.energy_log, f"Kx={Kx}, Ky={Ky}, Kz={Kz}, J={J}")
        log_message(runner.energy_log, f"hx={hx}, hy={hy}, hz={hz}, Lambda={Lambda}")
        log_message(runner.energy_log, f"Reference energy={reference_energy}")
        
    except Exception as e:
        print(f"模拟失败，参数: Model={model_type}, Lx={Lx}, Ly={Ly}, spin={spin}")
        print(f"Kx={Kx}, Ky={Ky}, Kz={Kz}, J={J}, hx={hx}, hy={hy}, hz={hz}, Lambda={Lambda}")
        print(f"Reference energy={reference_energy}")
        print(f"错误信息: {str(e)}")
        raise

def main():
    """主函数"""
    if len(sys.argv) != 14:
        print("使用方法: python train.py <model_type> <Lx> <Ly> <spin> <Kx> <Ky> <Kz> <J> <hx> <hy> <hz> <Lambda> <reference_energy>")
        print("模型类型: cRBM 或 ViT")
        print("参考能量: 用于显示相对误差，设为'None'则不显示")
        print("例如: python train.py cRBM 4 4 0.5 1.0 1.0 1.0 0.0 0.1 0.1 0.1 0.1 -6.396")
        print("例如: python train.py ViT 4 4 0.5 1.0 1.0 1.0 0.0 0.1 0.1 0.1 0.1 None")
        sys.exit(1)

    # 解析命令行参数
    try:
        model_type = sys.argv[1]
        Lx = int(sys.argv[2])
        Ly = int(sys.argv[3])
        spin = float(sys.argv[4])
        Kx = float(sys.argv[5])
        Ky = float(sys.argv[6])
        Kz = float(sys.argv[7])
        J = float(sys.argv[8])
        hx = float(sys.argv[9])
        hy = float(sys.argv[10])
        hz = float(sys.argv[11])
        Lambda = float(sys.argv[12])
        
        # 处理reference_energy参数
        ref_energy_str = sys.argv[13]
        if ref_energy_str.lower() == "none":
            reference_energy = None
        else:
            reference_energy = float(ref_energy_str)
            
    except ValueError as e:
        print(f"参数解析错误: {e}")
        print("请确保所有参数格式正确")
        sys.exit(1)

    # 验证参数合理性
    if Lx <= 0 or Ly <= 0:
        print("错误: 晶格尺寸必须为正整数")
        sys.exit(1)

    if model_type.lower() not in ["crbm", "vit"]:
        print("错误: 模型类型必须是 'cRBM' 或 'ViT'")
        sys.exit(1)

    print("="*70)
    print("Kitaev-Heisenberg模型模拟开始")
    print("="*70)
    print(f"模型类型: {model_type}")
    print(f"晶格尺寸: Lx={Lx}, Ly={Ly}")
    print(f"自旋大小: {spin}")
    print(f"Kitaev相互作用: Kx={Kx}, Ky={Ky}, Kz={Kz}")
    print(f"Heisenberg相互作用: J={J}")
    print(f"磁场[111]: hx={hx}, hy={hy}, hz={hz}")
    print(f"拉格朗日乘子: Lambda={Lambda}")
    print(f"参考能量: {reference_energy}")
    print("="*70)

    # 记录JAX设备信息
    print(f"JAX设备: {jax.devices()}")
    print(f"设备数量: {len(jax.devices())}")
    print("="*70)

    # 运行模拟
    start_time = time.time()
    run_simulation(model_type, Lx, Ly, spin, Kx, Ky, Kz, J, hx, hy, hz, Lambda, reference_energy)
    end_time = time.time()
    
    print("="*70)
    print(f"模拟完成，总用时: {end_time - start_time:.2f} 秒")
    print("="*70)

if __name__ == "__main__":
    main() 