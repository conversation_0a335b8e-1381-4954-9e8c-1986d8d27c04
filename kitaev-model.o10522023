Job start at: Thu Jun  5 18:24:51 +08 2025
Running on node: x1000c1s2b0n1
GPU Information:
Thu Jun  5 18:24:51 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.154.05             Driver Version: 535.154.05   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-40GB          On  | 00000000:81:00.0 Off |                    0 |
| N/A   42C    P0              52W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-40GB          On  | 00000000:C1:00.0 Off |                    0 |
| N/A   42C    P0              52W / 400W |      0MiB / 40960MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
==================== Job Configuration ====================
Model Type: ViT_cRBM
System Size: Lx=4, Ly=4
Spin: 0.5
Processing the following parameter combinations:
Kx values: 1.0
Ky values: 1.0
Kz values: 1.0
J values: 0.0
hx values: 0.1
hy values: 0.1
hz values: 0.1
Lambda values: 0.0
Reference Energy: -6.396
==========================================================
Starting computation Model=ViT_cRBM, Lx=4, Ly=4, Spin=0.5, Kx=1.0, Ky=1.0, Kz=1.0, J=0.0, hx=0.1, hy=0.1, hz=0.1, Lambda=0.0, RefE=-6.396 at: Thu Jun  5 18:24:51 +08 2025
Submitted job Model=ViT_cRBM, Lx=4, Ly=4, Spin=0.5, Kx=1.0, Ky=1.0, Kz=1.0, J=0.0, hx=0.1, hy=0.1, hz=0.1, Lambda=0.0, RefE=-6.396 at: Thu Jun  5 18:24:51 +08 2025
13:4: not a valid test operator: (
13:4: not a valid test operator: 535.154.05
======================================================================
Kitaev-Heisenberg模型模拟开始
======================================================================
模型类型: ViT_cRBM
晶格尺寸: Lx=4, Ly=4
自旋大小: 0.5
Kitaev相互作用: Kx=1.0, Ky=1.0, Kz=1.0
Heisenberg相互作用: J=0.0
磁场[111]: hx=0.1, hy=0.1, hz=0.1
拉格朗日乘子: Lambda=0.0
参考能量: -6.396
======================================================================
JAX设备: [CudaDevice(id=0), CudaDevice(id=1)]
设备数量: 2
======================================================================
Keyword argument edge_color is deprecated and does nothing anymore.
[2025-06-05 18:25:44] ==================================================
[2025-06-05 18:25:44] Kitaev-Heisenberg Model Study
[2025-06-05 18:25:44] ==================================================
[2025-06-05 18:25:44] Model type: ViT_cRBM
[2025-06-05 18:25:44] System parameters: Lx=4, Ly=4, N=32
[2025-06-05 18:25:44]   - Kitaev interactions: Kx=1.0, Ky=1.0, Kz=1.0
[2025-06-05 18:25:44]   - Heisenberg interaction: J=0.0
[2025-06-05 18:25:44]   - Magnetic field [111]: hx=0.1, hy=0.1, hz=0.1
[2025-06-05 18:25:44]   - Lagrange multiplier: Lambda=0.0
[2025-06-05 18:25:44]   - Reference energy: -6.396
[2025-06-05 18:26:05] --------------------------------------------------
[2025-06-05 18:26:05] Model parameters:
[2025-06-05 18:26:05]   - Model type: ViT_cRBM
[2025-06-05 18:26:05]   - cRBM Alpha: 2
[2025-06-05 18:26:05]   - Use hidden bias: True
[2025-06-05 18:26:05]   - Use visible bias: True
[2025-06-05 18:26:05]   - ViT Layers: 6
[2025-06-05 18:26:05]   - Model dimension: 64
[2025-06-05 18:26:05]   - Attention heads: 8
[2025-06-05 18:26:05]   - Patch size: 2
[2025-06-05 18:26:05]   - Parameter dtype: complex128
[2025-06-05 18:26:05]   - Use symmetries: True
[2025-06-05 18:26:05]   - Symmetries used: 32
[2025-06-05 18:26:05]   - Total parameters: 150897
[2025-06-05 18:26:05] --------------------------------------------------
[2025-06-05 18:26:05] Training parameters:
[2025-06-05 18:26:05]   - Learning rate: 0.08
[2025-06-05 18:26:05]   - Total annealing steps: 15000
[2025-06-05 18:26:05]   - Samples: 8192
[2025-06-05 18:26:05]   - Discarded samples: 0
[2025-06-05 18:26:05]   - Chunk size: 1024
[2025-06-05 18:26:05]   - Temperature range: [0.0, 1.0]
[2025-06-05 18:26:05]   - Restart period: 100
[2025-06-05 18:26:05]   - Period multiplier: 2.0
[2025-06-05 18:26:05]   - Gradient clipping: 1.0
[2025-06-05 18:26:05] --------------------------------------------------
[2025-06-05 18:26:05] Device status:
[2025-06-05 18:26:05]   - Number of devices: 2
[2025-06-05 18:26:05]   - Device type: NVIDIA A100-SXM4-40GB
[2025-06-05 18:26:05]   - NetKet experimental sharding: True
[2025-06-05 18:26:05] --------------------------------------------------
[2025-06-05 18:26:05] Start training...
模拟失败，参数: Model=ViT_cRBM, Lx=4, Ly=4, spin=0.5
Kx=1.0, Ky=1.0, Kz=1.0, J=0.0, hx=0.1, hy=0.1, hz=0.1, Lambda=0.0
Reference energy=-6.396
错误信息: 
                VMC_SRt requires a network with a number of parameters
                multiple of the number of MPI devices/ranks in use.

                You have a network with 150897, but
                there are 2 MPI ranks in use.

                To fix this, either add some 'fake' parameters to your
                network, or change the number of MPI nodes, or contribute
                some padding logic to NetKet!
                
Traceback (most recent call last):
  File "/home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/scripts/train.py", line 169, in <module>
    main() 
  File "/home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/scripts/train.py", line 161, in main
    run_simulation(model_type, Lx, Ly, spin, Kx, Ky, Kz, J, hx, hy, hz, Lambda, reference_energy)
  File "/home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/scripts/train.py", line 79, in run_simulation
    runner.run()
  File "/home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/src/runner.py", line 330, in run
    vmc = CustomFreeEnergyVMC_SRt(
  File "/home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/src/utils/FE_VMC_SRt.py", line 63, in __init__
    super().__init__(*args, **kwargs)
  File "/home/<USER>/ntu/s240076/Repositories/Projects/Kitaev_model/src/utils/FE_VMC_SRt.py", line 32, in __init__
    super().__init__(*args, **kwargs)
  File "/home/<USER>/ntu/s240076/.local/lib/python3.10/site-packages/netket/experimental/driver/vmc_srt.py", line 191, in __init__
    raise NotImplementedError(
NotImplementedError: 
                VMC_SRt requires a network with a number of parameters
                multiple of the number of MPI devices/ranks in use.

                You have a network with 150897, but
                there are 2 MPI ranks in use.

                To fix this, either add some 'fake' parameters to your
                network, or change the number of MPI nodes, or contribute
                some padding logic to NetKet!
                
Job finished at: Thu Jun  5 18:26:05 +08 2025
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Resource Usage on 2025-06-05 18:26:07.058027:
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	JobId: 10522023.pbs101
	Project: 12004256
	Exit Status: 0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	NCPUs: Requested(32), Used(32)
	CPU Time Used: 00:00:54
	Memory: Requested(220gb), Used(1619040kb)
	Vmem Used: 33200560kb
	Walltime: Requested(22:00:00), Used(00:01:40)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Execution Nodes Used: (x1000c1s2b0n1:ngpus=2:ncpus=32:mem=230686720kb)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	GPU Duration: 1.69mins
	GPU Power Consumed: 47.71W
	GPU Max GPU Memory Used: 840.0MB
	Memory Throughput Rate (Average): x1000c1s2b0n1:(gpu3:0%+gpu2:0%)
	Memory Throughput Rate (Max): x1000c1s2b0n1:(gpu3:0%+gpu2:0%)
	Memory Throughput Rate (Min): x1000c1s2b0n1:(gpu3:0%+gpu2:0%)
	GPU SM Utilization (Average): x1000c1s2b0n1:(gpu3:0%+gpu2:0%)
	GPU SM Utilization (Max): x1000c1s2b0n1:(gpu3:0%+gpu2:0%)
	GPU SM Utilization (Min): x1000c1s2b0n1:(gpu3:0%+gpu2:0%)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Warning: All GPUs have a percentage of 0 utilisation.
GPU application profile: Idle
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

